import sqlite3
import os
import sys

# تحديد مسار قاعدة البيانات
# يجب أن يكون هذا المسار متطابقًا مع ما يستخدمه التطبيق الرئيسي
DB_DIR = "db"
DB_PATH = os.path.join(DB_DIR, "smart_contracts.db")

template_name_to_delete = "فاتورة - Copy.docx"

def delete_template_from_db(name):
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # التحقق أولاً مما إذا كان القالب موجودًا وما إذا كانت هناك عقود مرتبطة به
        # في التطبيق، لدينا قيود ON DELETE RESTRICT لمنع حذف قالب مرتبط بعقود.
        # إذا كان المستخدم يريد الحذف "بكل تأكيد" حتى لو كان مرتبطًا، يمكن تعديل هذا.
        # لكن حاليًا، سنحترم قيود قاعدة البيانات.
        
        # الحصول على ID القالب
        cursor.execute("SELECT id FROM templates WHERE name = ?", (name,))
        template_id = cursor.fetchone()

        if template_id:
            template_id = template_id[0]
            # التحقق من وجود عقود مرتبطة
            cursor.execute("SELECT COUNT(*) FROM contracts WHERE template_id = ?", (template_id,))
            contract_count = cursor.fetchone()[0]

            if contract_count > 0:
                print(f"❌ لا يمكن حذف القالب '{name}' من قاعدة البيانات لأن هناك {contract_count} عقدًا مرتبطًا به.")
                print("يرجى حذف العقود المرتبطة بهذا القالب أولاً من واجهة المستخدم، أو تعديلها لربطها بقالب آخر.")
                # يمكنك هنا تنفيذ منطق حذف العقود المرتبطة أولاً إذا كنت متأكدًا من رغبة المستخدم
                # أو تجاهل قيود FK مؤقتًا (لا يوصى به)
            else:
                cursor.execute("DELETE FROM templates WHERE name = ?", (name,))
                conn.commit()
                print(f"✅ تم حذف ذكرى القالب '{name}' من قاعدة البيانات بنجاح.")
        else:
            print(f"❗ لم يتم العثور على القالب '{name}' في قاعدة البيانات.")

    except sqlite3.Error as e:
        print(f"❌ حدث خطأ في قاعدة البيانات أثناء الحذف: {e}")
    except Exception as e:
        print(f"❌ حدث خطأ غير متوقع: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    if not os.path.exists(DB_PATH):
        print(f"❌ ملف قاعدة البيانات غير موجود في المسار: {DB_PATH}")
        print("يرجى التأكد من تشغيل 'create_database.py' أولاً، ومن أن 'main_app.py' قد أنشأ قاعدة البيانات.")
        sys.exit(1) # الخروج برمز خطأ

    print(f"جاري محاولة حذف القالب '{template_name_to_delete}' من قاعدة البيانات...")
    delete_template_from_db(template_name_to_delete)
    print("\nيرجى الآن إعادة تشغيل برنامج 'main_app.py'.")