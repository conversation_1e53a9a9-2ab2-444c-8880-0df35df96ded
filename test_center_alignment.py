"""
اختبار محاذاة الواجهة في المنتصف
Test Center Alignment for UI Components
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة المسار الحالي لاستيراد الملفات المحلية
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from enhanced_ui_theme import enhanced_theme
    from enhanced_icons import icon_manager
    ENHANCED_MODULES_AVAILABLE = True
except ImportError:
    ENHANCED_MODULES_AVAILABLE = False
    print("تحذير: لم يتم العثور على ملفات التصميم المحسن")

class CenterAlignmentTest:
    """تطبيق اختبار محاذاة المنتصف"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🎯 اختبار محاذاة المنتصف - Center Alignment Test")
        self.root.geometry("1000x700")
        
        # تطبيق النظام المحسن إذا كان متاحاً
        if ENHANCED_MODULES_AVAILABLE:
            self.setup_enhanced_theme()
        else:
            self.setup_basic_theme()
        
        self.create_test_interface()
        
        # توسيط النافذة
        self.center_window()
    
    def setup_enhanced_theme(self):
        """إعداد النظام المحسن"""
        # تطبيق الألوان المحسنة
        self.root.configure(bg=enhanced_theme.colors['bg_primary'])
        
        # إعداد أنماط ttk
        self.style = enhanced_theme.setup_ttk_styles(self.root)
        
        # استخدام الألوان والخطوط من النظام المحسن
        self.colors = enhanced_theme.colors
        self.fonts = enhanced_theme.fonts
        
        print("✅ تم تطبيق النظام المحسن بنجاح")
    
    def setup_basic_theme(self):
        """إعداد النظام الأساسي كبديل"""
        self.root.configure(bg="#f8fafc")
        
        self.style = ttk.Style()
        
        # ألوان أساسية
        self.colors = {
            'primary': '#1e3a8a',
            'bg_primary': '#f8fafc',
            'bg_card': '#ffffff',
            'text_primary': '#1e293b',
            'success': '#059669',
            'warning': '#d97706',
            'error': '#dc2626',
            'border': '#e2e8f0'
        }
        
        # خطوط أساسية
        self.fonts = {
            'title': ('Arial', 16, 'bold'),
            'heading': ('Arial', 14, 'bold'),
            'body': ('Arial', 12, 'normal'),
            'button': ('Arial', 11, 'bold')
        }
        
        print("⚠️ تم تطبيق النظام الأساسي")
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_test_interface(self):
        """إنشاء واجهة اختبار المحاذاة"""
        
        # إطار رئيسي لتوسيط المحتوى
        main_container = ttk.Frame(self.root)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي مع محاذاة وسط
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))
        
        title_label = ttk.Label(title_frame, 
                               text="🎯 اختبار محاذاة المنتصف",
                               style="Title.TLabel" if ENHANCED_MODULES_AVAILABLE else None,
                               font=self.fonts['title'])
        title_label.pack(anchor="center")
        
        # إنشاء دفتر التبويبات مع محاذاة وسط
        self.notebook = ttk.Notebook(main_container, style="Modern.TNotebook" if ENHANCED_MODULES_AVAILABLE else None)
        self.notebook.pack(fill="both", expand=True, anchor="center")
        
        # تبويب اختبار الأزرار
        self.create_buttons_test_tab()
        
        # تبويب اختبار النماذج
        self.create_forms_test_tab()
        
        # تبويب اختبار البطاقات
        self.create_cards_test_tab()
    
    def create_buttons_test_tab(self):
        """تبويب اختبار الأزرار"""
        buttons_tab = ttk.Frame(self.notebook)
        self.notebook.add(buttons_tab, text="🔘 اختبار الأزرار")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(buttons_tab, bg=self.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(buttons_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # العنوان مع محاذاة وسط
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))
        
        title_label = ttk.Label(title_frame, text="اختبار محاذاة الأزرار", 
                               font=self.fonts['heading'])
        title_label.pack(anchor="center")
        
        # إطار الأزرار مع محاذاة وسط
        buttons_container = ttk.Frame(scrollable_frame, style="Card.TFrame" if ENHANCED_MODULES_AVAILABLE else None, padding="20")
        buttons_container.pack(fill="x", padx=40, pady=(0, 20), anchor="center")
        
        # مجموعة أزرار في المنتصف
        buttons_frame = ttk.Frame(buttons_container)
        buttons_frame.pack(anchor="center")
        
        if ENHANCED_MODULES_AVAILABLE:
            # أزرار محسنة
            ttk.Button(buttons_frame, text="🔵 زر أساسي", 
                      style="Primary.TButton").pack(side="left", padx=10)
            ttk.Button(buttons_frame, text="✅ زر النجاح", 
                      style="Success.TButton").pack(side="left", padx=10)
            ttk.Button(buttons_frame, text="⚠️ زر التحذير", 
                      style="Warning.TButton").pack(side="left", padx=10)
            ttk.Button(buttons_frame, text="❌ زر الخطر", 
                      style="Danger.TButton").pack(side="left", padx=10)
        else:
            # أزرار أساسية
            ttk.Button(buttons_frame, text="زر أساسي").pack(side="left", padx=10)
            ttk.Button(buttons_frame, text="زر ثانوي").pack(side="left", padx=10)
    
    def create_forms_test_tab(self):
        """تبويب اختبار النماذج"""
        forms_tab = ttk.Frame(self.notebook)
        self.notebook.add(forms_tab, text="📝 اختبار النماذج")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(forms_tab, bg=self.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(forms_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # العنوان مع محاذاة وسط
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))
        
        title_label = ttk.Label(title_frame, text="اختبار محاذاة النماذج", 
                               font=self.fonts['heading'])
        title_label.pack(anchor="center")
        
        # نموذج اختبار مع محاذاة وسط
        form_frame = ttk.LabelFrame(scrollable_frame, text="نموذج اختبار", padding="20")
        form_frame.pack(fill="x", padx=40, pady=(0, 20), anchor="center")
        
        # حقول النموذج
        fields = [
            ("الاسم:", "اسم المستخدم"),
            ("البريد الإلكتروني:", "<EMAIL>"),
            ("الهاتف:", "0501234567"),
            ("العنوان:", "الرياض، المملكة العربية السعودية")
        ]
        
        for i, (label_text, placeholder) in enumerate(fields):
            field_frame = ttk.Frame(form_frame)
            field_frame.pack(fill="x", pady=5, anchor="center")
            
            label = ttk.Label(field_frame, text=label_text, 
                             font=self.fonts['body'])
            label.pack(side="right", padx=(0, 10))
            
            entry = ttk.Entry(field_frame, 
                             style="Modern.TEntry" if ENHANCED_MODULES_AVAILABLE else None,
                             font=self.fonts['body'])
            entry.pack(side="right", fill="x", expand=True)
            entry.insert(0, placeholder)
        
        # أزرار النموذج مع محاذاة وسط
        form_buttons = ttk.Frame(form_frame)
        form_buttons.pack(anchor="center", pady=(15, 0))
        
        if ENHANCED_MODULES_AVAILABLE:
            ttk.Button(form_buttons, text="💾 حفظ", 
                      style="Success.TButton").pack(side="left", padx=10)
            ttk.Button(form_buttons, text="❌ إلغاء", 
                      style="Danger.TButton").pack(side="left", padx=10)
        else:
            ttk.Button(form_buttons, text="حفظ").pack(side="left", padx=10)
            ttk.Button(form_buttons, text="إلغاء").pack(side="left", padx=10)
    
    def create_cards_test_tab(self):
        """تبويب اختبار البطاقات"""
        cards_tab = ttk.Frame(self.notebook)
        self.notebook.add(cards_tab, text="🃏 اختبار البطاقات")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(cards_tab, bg=self.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(cards_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # العنوان مع محاذاة وسط
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))
        
        title_label = ttk.Label(title_frame, text="اختبار محاذاة البطاقات", 
                               font=self.fonts['heading'])
        title_label.pack(anchor="center")
        
        # إطار البطاقات مع محاذاة وسط
        cards_container = ttk.Frame(scrollable_frame)
        cards_container.pack(fill="both", expand=True, padx=40, pady=(0, 40), anchor="center")
        
        # بطاقات اختبار
        for i in range(3):
            card_frame = ttk.LabelFrame(cards_container, text=f"بطاقة {i+1}", padding="15")
            card_frame.pack(fill="x", pady=10, anchor="center")
            
            # محتوى البطاقة مع محاذاة وسط
            content_frame = ttk.Frame(card_frame)
            content_frame.pack(anchor="center")
            
            ttk.Label(content_frame, text=f"هذه بطاقة اختبار رقم {i+1}", 
                     font=self.fonts['body']).pack(anchor="center", pady=5)
            
            if ENHANCED_MODULES_AVAILABLE:
                ttk.Button(content_frame, text="عرض التفاصيل", 
                          style="Primary.TButton").pack(anchor="center", pady=5)
            else:
                ttk.Button(content_frame, text="عرض التفاصيل").pack(anchor="center", pady=5)

def main():
    """تشغيل تطبيق اختبار المحاذاة"""
    root = tk.Tk()
    app = CenterAlignmentTest(root)
    
    print("🚀 تم تشغيل تطبيق اختبار محاذاة المنتصف")
    print("📋 يمكنك الآن فحص محاذاة العناصر في التبويبات المختلفة")
    
    root.mainloop()

if __name__ == "__main__":
    main()
