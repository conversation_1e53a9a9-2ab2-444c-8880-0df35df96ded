# أرشيف التوثيق والتقارير

---
## README.md
---

# نظام إدارة العقود الذكي

نظام شامل لإدارة العقود وتوليدها تلقائياً باستخدام قوالب Word مع واجهة رسومية سهلة الاستخدام.

## 🌟 الميزات الرئيسية

### 👥 إدارة العملاء
- إضافة عملاء جدد مع بيانات مفصلة (الاسم، الهاتف، العنوان، إلخ)
- عرض قائمة العملاء في جدول منظم
- حذف العملاء غير المرغوب فيهم
- دعم الألقاب المختلفة (السيد، السيدة، الدكتور، إلخ)

### 📄 إدارة القوالب
- رفع قوالب Word (.docx) بسهولة
- استخراج المتغيرات من القوالب تلقائياً
- عرض المتغيرات الموجودة في كل قالب
- حذف القوالب غير المستخدمة

### 📑 توليد العقود
- اختيار العميل والقالب المطلوب
- ملء البيانات تلقائياً من معلومات العميل
- إضافة التواريخ (ميلادي وهجري) تلقائياً
- **معاينة مباشرة للعقد** في نافذة داخلية
- **تحديث المعاينة أثناء الكتابة** في الحقول
- حفظ العقود المولدة في مجلد الأرشيف
- تسجيل جميع العقود في قاعدة البيانات

## 🛠️ المتطلبات

- Python 3.7 أو أحدث
- نظام التشغيل Windows
- المكتبات المطلوبة (يتم تثبيتها تلقائياً):
  - `docxtpl` - لمعالجة قوالب Word
  - `hijri-converter` - لتحويل التواريخ الهجرية

## 🚀 التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
# تشغيل ملف تثبيت المتطلبات
install_requirements.bat
```

### 2. إنشاء قاعدة البيانات
```bash
# تشغيل ملف إنشاء قاعدة البيانات
python create_database.py
```

### 3. تشغيل التطبيق
```bash
# تشغيل التطبيق
run.bat
```

## 📁 هيكل المشروع

```
برنامج العقود/
├── main_app.py                    # الملف الرئيسي للتطبيق
├── create_database.py             # إنشاء قاعدة البيانات
├── install_requirements.bat       # تثبيت المتطلبات
├── run.bat                       # تشغيل التطبيق
├── requirements.txt              # قائمة المتطلبات
├── README.md                     # ملف التوثيق
├── db/                          # مجلد قاعدة البيانات
│   └── smart_contracts.db       # ملف قاعدة البيانات
├── templates/                   # مجلد قوالب Word
├── archives/                    # مجلد العقود المولدة
└── error.log                    # ملف سجل الأخطاء
```

## 🎯 كيفية الاستخدام

### إضافة عميل جديد
1. انتقل إلى تبويب 

---
## خطأ في رفع القالب: عقد_صيانه.docx - 2025-07-08 02:49:29
---

**الخطأ:**
```
'C:/Users/<USER>/Desktop/smart_contracts_system/برنامج العقود/templates/عقد_صيانه.docx' and 'templates\\عقد_صيانه.docx' are the same file
```


---
## خطأ في رفع القالب: عقد_صيانه.docx - 2025-07-08 02:50:35
---

**الخطأ:**
```
'C:/Users/<USER>/Desktop/smart_contracts_system/برنامج العقود/templates/عقد_صيانه.docx' and 'templates\\عقد_صيانه.docx' are the same file
```


---
## خطأ في رفع القالب: عقد_صيانه.docx - 2025-07-08 02:53:10
---

**الخطأ:**
```
'C:/Users/<USER>/Desktop/smart_contracts_system/برنامج العقود/templates/عقد_صيانه.docx' and 'templates\\عقد_صيانه.docx' are the same file
```


---
## خطأ في رفع القالب: عقد_صيانه.docx - 2025-07-08 02:55:04
---

**الخطأ:**
```
'C:/Users/<USER>/Desktop/smart_contracts_system/برنامج العقود/templates/عقد_صيانه.docx' and 'templates\\عقد_صيانه.docx' are the same file
```


---
## خطأ في قراءة متغيرات القالب: مسلسل.docx - 2025-07-08 04:35:43
---

**ملف القالب:** `templates\مسلسل.docx`
**الخطأ:**
```
Package not found at 'templates\مسلسل.docx'
```


---
## خطأ في قراءة متغيرات القالب: مسلسل.docx - 2025-07-08 04:36:24
---

**ملف القالب:** `templates\مسلسل.docx`
**الخطأ:**
```
Package not found at 'templates\مسلسل.docx'
```


---
## خطأ في قراءة متغيرات القالب: مسلسل.docx - 2025-07-08 04:41:09
---

**ملف القالب:** `templates\مسلسل.docx`
**الخطأ:**
```
Package not found at 'templates\مسلسل.docx'
```


---
## خطأ في رفع القالب: فاتـورة.docx - 2025-07-08 12:52:42
---

**الخطأ:**
```
Encountered unknown tag 'endfor'.
```


---
## خطأ في رفع القالب: جدول_فاتورة_ديناميكي_مسلسل_يمين.docx - 2025-07-08 12:54:30
---

**الخطأ:**
```
Encountered unknown tag 'endfor'.
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-08 12:56:39
---

**الخطأ:**
```
Encountered unknown tag 'endfor'.
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-08 13:03:01
---

**الخطأ:**
```
Encountered unknown tag 'endfor'.
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-08 13:03:08
---

**الخطأ:**
```
Encountered unknown tag 'endfor'.
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-08 13:08:12
---

**الخطأ:**
```
Encountered unknown tag 'endfor'.
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-09 01:40:02
---

**الخطأ:**
```
'NoneType' object does not support item assignment
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-10 04:18:50
---

**الخطأ:**
```
unexpected char '×' at 32097
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-10 04:18:56
---

**الخطأ:**
```
unexpected char '×' at 32097
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-10 04:23:07
---

**الخطأ:**
```
unexpected char '×' at 32097
```


---
## خطأ في رفع القالب: فاتورة.docx - 2025-07-10 04:23:51
---

**الخطأ:**
```
unexpected char '×' at 32097
```


---
## خطأ في توليد العقد: حسام احمد_فاتورة_20250712_130644.docx - 2025-07-12 13:06:52
---

**الخطأ:**
```
cannot unpack non-iterable PilImage object
```


---
## خطأ في توليد العقد: حسام احمد_فاتورة_20250712_130743.docx - 2025-07-12 13:08:02
---

**الخطأ:**
```
cannot unpack non-iterable PilImage object
```
