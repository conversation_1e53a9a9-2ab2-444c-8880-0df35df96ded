"""
مجموعة الأيقونات المحسنة لبرنامج العقود الذكي
Enhanced Icons Collection for Smart Contracts Application
"""

import tkinter as tk
from tkinter import ttk

class IconManager:
    """مدير الأيقونات والعناصر البصرية"""
    
    def __init__(self):
        """تهيئة مجموعة الأيقونات"""
        
        # أيقونات Unicode للاستخدام في الواجهة
        self.icons = {
            # أيقونات التنقل والقوائم
            'home': '🏠',
            'dashboard': '📊',
            'menu': '☰',
            'settings': '⚙️',
            'search': '🔍',
            'filter': '🔽',
            'sort': '↕️',
            'refresh': '🔄',
            'close': '✖️',
            'minimize': '➖',
            'maximize': '⬜',
            
            # أيقونات العمليات
            'add': '➕',
            'edit': '✏️',
            'delete': '🗑️',
            'save': '💾',
            'copy': '📋',
            'paste': '📄',
            'cut': '✂️',
            'undo': '↶',
            'redo': '↷',
            'print': '🖨️',
            'export': '📤',
            'import': '📥',
            'download': '⬇️',
            'upload': '⬆️',
            
            # أيقونات الملفات والمستندات
            'file': '📄',
            'folder': '📁',
            'document': '📃',
            'contract': '📋',
            'template': '📝',
            'pdf': '📕',
            'word': '📘',
            'excel': '📗',
            'image': '🖼️',
            'archive': '📦',
            
            # أيقونات المستخدمين والعملاء
            'user': '👤',
            'users': '👥',
            'client': '🧑‍💼',
            'company': '🏢',
            'team': '👨‍👩‍👧‍👦',
            'profile': '👤',
            'contact': '📞',
            'email': '📧',
            'phone': '📱',
            'address': '🏠',
            
            # أيقونات المالية والأرقام
            'money': '💰',
            'price': '💲',
            'tax': '🧾',
            'invoice': '🧾',
            'payment': '💳',
            'bank': '🏦',
            'calculator': '🧮',
            'chart': '📈',
            'report': '📊',
            'statistics': '📉',
            
            # أيقونات الحالة والتنبيهات
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'question': '❓',
            'exclamation': '❗',
            'check': '✓',
            'cross': '✗',
            'star': '⭐',
            'flag': '🚩',
            
            # أيقونات التاريخ والوقت
            'calendar': '📅',
            'clock': '🕐',
            'date': '📆',
            'time': '⏰',
            'schedule': '📋',
            'deadline': '⏳',
            'history': '📜',
            'recent': '🕒',
            
            # أيقونات التقنية والأدوات
            'tool': '🔧',
            'gear': '⚙️',
            'code': '💻',
            'database': '🗄️',
            'server': '🖥️',
            'network': '🌐',
            'security': '🔒',
            'key': '🔑',
            'lock': '🔐',
            'unlock': '🔓',
            
            # أيقونات التواصل والمشاركة
            'share': '📤',
            'send': '📨',
            'receive': '📨',
            'message': '💬',
            'chat': '💭',
            'notification': '🔔',
            'bell': '🔔',
            'mail': '✉️',
            
            # أيقونات خاصة بالعقود
            'contract_new': '📋',
            'contract_signed': '✍️',
            'contract_pending': '⏳',
            'contract_approved': '✅',
            'contract_rejected': '❌',
            'signature': '✍️',
            'seal': '🏷️',
            'legal': '⚖️',
            'law': '📚',
            'agreement': '🤝',
            
            # أيقونات QR والباركود
            'qr_code': '📱',
            'barcode': '📊',
            'scan': '📷',
            'camera': '📸',
            
            # أيقونات إضافية
            'location': '📍',
            'map': '🗺️',
            'building': '🏢',
            'office': '🏬',
            'factory': '🏭',
            'warehouse': '🏪',
            'truck': '🚚',
            'delivery': '📦',
            'shipping': '🚢',
            
            # أيقونات الحالة المتقدمة
            'online': '🟢',
            'offline': '🔴',
            'busy': '🟡',
            'away': '🟠',
            'available': '🟢',
            'unavailable': '🔴',
            
            # أيقونات التصنيف والتنظيم
            'category': '📂',
            'tag': '🏷️',
            'label': '🔖',
            'bookmark': '📑',
            'pin': '📌',
            'clip': '📎',
            
            # أيقونات الجودة والتقييم
            'quality': '💎',
            'rating': '⭐',
            'review': '📝',
            'feedback': '💬',
            'comment': '💭',
            'note': '📝',
            
            # أيقونات الأمان والحماية
            'shield': '🛡️',
            'protection': '🔒',
            'privacy': '🔐',
            'secure': '🔒',
            'verified': '✅',
            'certified': '🏆',
            
            # أيقونات التطوير والتحديث
            'update': '🔄',
            'upgrade': '⬆️',
            'version': '🔢',
            'release': '🚀',
            'beta': '🧪',
            'test': '🧪',
            
            # أيقونات الطاقة والحالة
            'power': '⚡',
            'battery': '🔋',
            'energy': '⚡',
            'signal': '📶',
            'wifi': '📶',
            'connection': '🔗',
            
            # أيقونات الطقس والبيئة
            'weather': '🌤️',
            'sun': '☀️',
            'cloud': '☁️',
            'rain': '🌧️',
            'snow': '❄️',
            'wind': '💨',
            
            # أيقونات الترفيه والوسائط
            'play': '▶️',
            'pause': '⏸️',
            'stop': '⏹️',
            'record': '⏺️',
            'volume': '🔊',
            'mute': '🔇',
            'music': '🎵',
            'video': '🎬',
            
            # أيقونات النقل والمواصلات
            'car': '🚗',
            'bus': '🚌',
            'train': '🚆',
            'plane': '✈️',
            'ship': '🚢',
            'bike': '🚲',
            'walk': '🚶',
            'taxi': '🚕',
        }
        
        # ألوان الأيقونات حسب النوع
        self.icon_colors = {
            'success': '#059669',
            'error': '#dc2626',
            'warning': '#d97706',
            'info': '#0284c7',
            'primary': '#1e3a8a',
            'secondary': '#64748b',
            'accent': '#f59e0b',
            'neutral': '#6b7280',
        }
    
    def get_icon(self, name, fallback='📄'):
        """الحصول على أيقونة بالاسم مع أيقونة احتياطية"""
        return self.icons.get(name, fallback)
    
    def create_icon_label(self, parent, icon_name, text="", color=None, font_size=16, **kwargs):
        """إنشاء تسمية مع أيقونة"""
        icon = self.get_icon(icon_name)
        
        if text:
            display_text = f"{icon} {text}"
        else:
            display_text = icon
        
        label = tk.Label(parent, text=display_text, **kwargs)
        
        if color:
            label.configure(fg=color)
        
        return label
    
    def create_icon_button(self, parent, icon_name, text="", command=None, style="Primary.TButton", **kwargs):
        """إنشاء زر مع أيقونة"""
        icon = self.get_icon(icon_name)
        
        if text:
            display_text = f"{icon} {text}"
        else:
            display_text = icon
        
        button = ttk.Button(parent, text=display_text, command=command, style=style, **kwargs)
        return button
    
    def get_status_icon(self, status):
        """الحصول على أيقونة الحالة"""
        status_icons = {
            'active': self.get_icon('online'),
            'inactive': self.get_icon('offline'),
            'pending': self.get_icon('clock'),
            'approved': self.get_icon('success'),
            'rejected': self.get_icon('error'),
            'draft': self.get_icon('edit'),
            'completed': self.get_icon('check'),
            'cancelled': self.get_icon('cross'),
            'in_progress': self.get_icon('clock'),
            'new': self.get_icon('star'),
        }
        return status_icons.get(status, self.get_icon('question'))
    
    def get_file_icon(self, file_extension):
        """الحصول على أيقونة نوع الملف"""
        file_icons = {
            '.pdf': self.get_icon('pdf'),
            '.doc': self.get_icon('word'),
            '.docx': self.get_icon('word'),
            '.xls': self.get_icon('excel'),
            '.xlsx': self.get_icon('excel'),
            '.png': self.get_icon('image'),
            '.jpg': self.get_icon('image'),
            '.jpeg': self.get_icon('image'),
            '.gif': self.get_icon('image'),
            '.txt': self.get_icon('document'),
            '.zip': self.get_icon('archive'),
            '.rar': self.get_icon('archive'),
        }
        return file_icons.get(file_extension.lower(), self.get_icon('file'))
    
    def create_status_indicator(self, parent, status, text="", **kwargs):
        """إنشاء مؤشر حالة ملون"""
        icon = self.get_status_icon(status)
        
        # تحديد لون الحالة
        status_colors = {
            'active': self.icon_colors['success'],
            'approved': self.icon_colors['success'],
            'completed': self.icon_colors['success'],
            'inactive': self.icon_colors['error'],
            'rejected': self.icon_colors['error'],
            'cancelled': self.icon_colors['error'],
            'pending': self.icon_colors['warning'],
            'in_progress': self.icon_colors['warning'],
            'draft': self.icon_colors['info'],
            'new': self.icon_colors['accent'],
        }
        
        color = status_colors.get(status, self.icon_colors['neutral'])
        
        if text:
            display_text = f"{icon} {text}"
        else:
            display_text = icon
        
        label = tk.Label(parent, text=display_text, fg=color, **kwargs)
        return label

# إنشاء مثيل عام لمدير الأيقونات
icon_manager = IconManager()
