@echo off
chcp 65001 > nul
echo.
echo ====================================================
echo.
echo   ملخص المشكلة والحل - نظام إدارة العقود الذكي
echo.
echo ====================================================
echo.
echo المشكلة الحالية:
echo -----------------
echo تواجه مشكلة في حفظ بيانات العملاء، وتظهر رسالة خطأ مثل "no such column: tax_number".
echo هذا يعني أن عمود "الرقم الضريبي" (tax_number) غير موجود في جدول العملاء بقاعدة البيانات.
echo.
echo السبب:
echo -------
echo تم تعديل هيكل قاعدة البيانات في الكود البرمجي ليشمل عمود "الرقم الضريبي"،
echo ولكن هذا التعديل لم يتم تطبيقه على ملف قاعدة البيانات الحالي لديك (smart_contracts.db).
echo التعديلات على ملفات Python لا تحدث قاعدة البيانات تلقائياً.
echo.
echo الحل (هام جداً):
echo -----------------
echo يجب عليك إعادة إنشاء قاعدة البيانات لتطبيق التغييرات.
echo.
echo تحذير: هذه العملية ستحذف جميع بيانات العملاء والقوالب والعقود الحالية.
echo إذا كانت لديك بيانات مهمة، يرجى عمل نسخة احتياطية من مجلد "db" قبل المتابعة.
echo.
echo خطوات الحل:
echo -----------
echo 1.  اغلق برنامج العقود الذكي تماماً.
echo 2.  احذف مجلد "db" بالكامل. ستجده في نفس مجلد هذا الملف:
echo     C:\Users\<USER>\Desktop\smart_contracts_system\برنامج العقود\db
echo 3.  بعد حذف مجلد "db"، قم بتشغيل ملف "create_database.py" لإنشاء قاعدة بيانات جديدة.
echo     يمكنك تشغيله بفتح موجه الأوامر (Command Prompt) والانتقال إلى مجلد المشروع ثم كتابة:
echo     python create_database.py
echo     (أو يمكنك تشغيل ملف "setup.bat" إذا كان موجوداً في مجلد المشروع)
echo.
echo 4.  بعد أن ترى رسالة "تم إنشاء قاعدة البيانات والجداول بنجاح" في موجه الأوامر،
echo     يمكنك الآن تشغيل برنامج العقود الذكي مرة أخرى.
echo.
echo ====================================================
echo.
echo   اضغط اي مفتاح للإغلاق.
echo.
echo ====================================================
pause > nul