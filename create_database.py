import sqlite3
import os

# المسارات والمجلدات
DB_DIR = "db"
DB_PATH = os.path.join(DB_DIR, "smart_contracts.db")

def create_database():
    """
    ينشئ قاعدة البيانات والجداول اللازمة إذا لم تكن موجودة.
    """
    # التأكد من وجود مجلد db
    if not os.path.exists(DB_DIR):
        os.makedirs(DB_DIR)
        print(f"تم إنشاء مجلد: {DB_DIR}")

    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # جدول العملاء
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,        -- اللقب (مثال: السيد، السيدة، مؤسسة، شركة)
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            city TEXT,       -- المدينة
            district TEXT,   -- المنطقة
            street TEXT,     -- الشارع
            building TEXT,   -- المبنى
            tax_number TEXT,  -- الرقم الضريبي
            created_at TEXT NOT NULL
        )
        """)

        # جدول قوالب العقود
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            variables TEXT,
            created_at TEXT NOT NULL
        )
        """)

        # جدول العقود المولدة
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS contracts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            agreement_number INTEGER UNIQUE NOT NULL,
            client_id INTEGER NOT NULL,
            template_id INTEGER NOT NULL,
            contract_data TEXT NOT NULL, -- JSON data of variables used for generation
            word_file_path TEXT NOT NULL, -- Path to the generated Word document
            created_at TEXT NOT NULL,
            FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE,
            FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE RESTRICT
        )
        """)

        conn.commit()
        conn.close()
        print(f"✅ قاعدة البيانات والجداول تم إنشاؤها بنجاح في: {DB_PATH}")

    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        # يمكن هنا تسجيل الخطأ في ملف logs إذا أردت

if __name__ == "__main__":
    create_database()