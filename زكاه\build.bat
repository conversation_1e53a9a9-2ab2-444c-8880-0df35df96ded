@echo off
chcp 65001 > nul
setlocal

:: --- اسم ملف البايثون المصدر ---
set SCRIPT_NAME=zatca_generator.py
set EXE_NAME=ZATCA_QR_Generator

echo.
echo =================================================================
echo.
echo           ZATCA QR Code Generator - Builder Script
echo.
echo =================================================================
echo.
echo هذا السكربت سيقوم بتثبيت المتطلبات وبناء ملف تنفيذي واحد.
echo.

:: التحقق من وجود ملف البايثون
if not exist "%SCRIPT_NAME%" (
    echo [ERROR] لم يتم العثور على الملف المصدر '%SCRIPT_NAME%'.
    echo الرجاء التأكد من حفظ كود البايثون في نفس المجلد بهذا الاسم.
    pause
    exit /b
)

:: التحقق من وجود بايثون
echo [1/4] التحقق من وجود بايثون...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] لم يتم تثبيت بايثون او لم يتم اضافته الى مسار النظام (PATH).
    echo الرجاء تثبيت بايثون اولاً.
    pause
    exit /b
)
echo      ... تم العثور على بايثون.

:: تثبيت/تحديث المكتبات المطلوبة
echo [2/4] تثبيت وتحديث المكتبات المطلوبة (qrcode, pyinstaller)...
pip install --upgrade qrcode[pil] pyinstaller > nul
echo      ... تم تثبيت المكتبات بنجاح.

:: بناء الملف التنفيذي
echo [3/4] جاري بناء الملف التنفيذي (%EXE_NAME%.exe)...
pyinstaller --onefile --name "%EXE_NAME%" "%SCRIPT_NAME%"
echo      ... اكتمل بناء الملف التنفيذي.

:: تنظيف الملفات المؤقتة
echo [4/4] جاري تنظيف الملفات المؤقتة...
rmdir /s /q build > nul 2> nul
del "%EXE_NAME%.spec" > nul 2> nul
echo      ... تم التنظيف.


echo.
echo =================================================================
echo.
echo           *** اكتملت العملية بنجاح! ***
echo.
echo.
echo  - تم انشاء الملف التنفيذي: %EXE_NAME%.exe
echo  - يمكنك العثور عليه في المجلد التالي:
echo    %cd%\dist
echo.
echo  - يمكنك الآن نقل هذا الملف إلى أي جهاز كمبيوتر (ويندوز) وتشغيله
echo    مباشرة دون الحاجة لتثبيت أي شيء.
echo.
echo =================================================================
echo.

pause
