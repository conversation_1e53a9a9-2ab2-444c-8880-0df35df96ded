/* Default light theme */
body {
    background-color: #ffffff;
    color: #000000;
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

/* Dark theme */
body.dark-mode {
    background-color: #121212;
    color: #ffffff;
}

/* Common styles */
header, footer {
    padding: 1rem;
    text-align: center;
}

header {
    background-color: #f0f0f0;
    color: #333333;
}

footer {
    background-color: #333333;
    color: #f0f0f0;
}

body.dark-mode header {
    background-color: #1f1f1f;
    color: #ffffff;
}

body.dark-mode footer {
    background-color: #1f1f1f;
    color: #ffffff;
}

button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

button.light-mode {
    background-color: #ffffff;
    color: #000000;
    border: 1px solid #cccccc;
}

button.dark-mode {
    background-color: #333333;
    color: #ffffff;
    border: 1px solid #444444;
}

/* Add transitions for smooth theme switching */
body, header, footer, button {
    transition: background-color 0.3s, color 0.3s;
}
