"""
اختبار التصميم المحسن لبرنامج العقود الذكي
Test Enhanced Design for Smart Contracts Application
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة المسار الحالي لاستيراد الملفات المحلية
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from enhanced_ui_theme import enhanced_theme
    from enhanced_icons import icon_manager
    ENHANCED_MODULES_AVAILABLE = True
except ImportError:
    ENHANCED_MODULES_AVAILABLE = False
    print("تحذير: لم يتم العثور على ملفات التصميم المحسن")

class DesignTestApp:
    """تطبيق اختبار التصميم المحسن"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🎨 اختبار التصميم المحسن - Enhanced Design Test")
        self.root.geometry("1000x700")
        
        # تطبيق النظام المحسن إذا كان متاحاً
        if ENHANCED_MODULES_AVAILABLE:
            self.setup_enhanced_theme()
        else:
            self.setup_basic_theme()
        
        self.create_test_interface()
        
        # توسيط النافذة
        self.center_window()
    
    def setup_enhanced_theme(self):
        """إعداد النظام المحسن"""
        # تطبيق الألوان المحسنة
        self.root.configure(bg=enhanced_theme.colors['bg_primary'])
        
        # إعداد أنماط ttk
        self.style = enhanced_theme.setup_ttk_styles(self.root)
        
        # استخدام الألوان والخطوط من النظام المحسن
        self.colors = enhanced_theme.colors
        self.fonts = enhanced_theme.fonts
        
        print("✅ تم تطبيق النظام المحسن بنجاح")
    
    def setup_basic_theme(self):
        """إعداد النظام الأساسي كبديل"""
        self.root.configure(bg="#f8fafc")
        
        self.style = ttk.Style()
        
        # ألوان أساسية
        self.colors = {
            'primary': '#1e3a8a',
            'bg_primary': '#f8fafc',
            'bg_card': '#ffffff',
            'text_primary': '#1e293b',
            'success': '#059669',
            'warning': '#d97706',
            'error': '#dc2626',
            'border': '#e2e8f0'
        }
        
        # خطوط أساسية
        self.fonts = {
            'title': ('Arial', 16, 'bold'),
            'heading': ('Arial', 14, 'bold'),
            'body': ('Arial', 12, 'normal'),
            'button': ('Arial', 11, 'bold')
        }
        
        print("⚠️ تم تطبيق النظام الأساسي")
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill="both", expand=True)
        
        # العنوان الرئيسي
        title_label = ttk.Label(main_frame, 
                               text="🎨 اختبار التصميم المحسن لبرنامج العقود",
                               style="Title.TLabel" if ENHANCED_MODULES_AVAILABLE else None,
                               font=self.fonts['title'])
        title_label.pack(pady=(0, 20))
        
        # إنشاء دفتر التبويبات
        self.create_tabs(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء تبويبات الاختبار"""
        
        # دفتر التبويبات
        notebook = ttk.Notebook(parent, style="Modern.TNotebook" if ENHANCED_MODULES_AVAILABLE else None)
        notebook.pack(fill="both", expand=True)
        
        # تبويب الألوان
        colors_tab = ttk.Frame(notebook)
        notebook.add(colors_tab, text="🎨 الألوان")
        self.create_colors_test(colors_tab)
        
        # تبويب الأزرار
        buttons_tab = ttk.Frame(notebook)
        notebook.add(buttons_tab, text="🔘 الأزرار")
        self.create_buttons_test(buttons_tab)
        
        # تبويب النماذج
        forms_tab = ttk.Frame(notebook)
        notebook.add(forms_tab, text="📝 النماذج")
        self.create_forms_test(forms_tab)
        
        # تبويب الجداول
        tables_tab = ttk.Frame(notebook)
        notebook.add(tables_tab, text="📊 الجداول")
        self.create_tables_test(tables_tab)
        
        # تبويب الأيقونات
        if ENHANCED_MODULES_AVAILABLE:
            icons_tab = ttk.Frame(notebook)
            notebook.add(icons_tab, text="🔣 الأيقونات")
            self.create_icons_test(icons_tab)
    
    def create_colors_test(self, parent):
        """اختبار الألوان"""
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(parent, bg=self.colors['bg_primary'])
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # عرض الألوان
        ttk.Label(scrollable_frame, text="عينات الألوان", 
                 font=self.fonts['heading']).pack(pady=10)
        
        colors_frame = ttk.Frame(scrollable_frame)
        colors_frame.pack(fill="x", padx=20, pady=10)
        
        # عرض الألوان في شبكة
        row = 0
        col = 0
        for color_name, color_value in self.colors.items():
            color_frame = tk.Frame(colors_frame, bg=color_value, 
                                 width=120, height=80, relief="solid", borderwidth=1)
            color_frame.grid(row=row, column=col, padx=5, pady=5)
            color_frame.grid_propagate(False)
            
            # تسمية اللون
            text_color = "#ffffff" if color_name in ['primary', 'error', 'text_primary'] else "#000000"
            label = tk.Label(color_frame, text=color_name, bg=color_value, 
                           fg=text_color, font=self.fonts['small'])
            label.pack(expand=True)
            
            col += 1
            if col > 4:
                col = 0
                row += 1
    
    def create_buttons_test(self, parent):
        """اختبار الأزرار"""
        
        frame = ttk.Frame(parent, padding="20")
        frame.pack(fill="both", expand=True)
        
        ttk.Label(frame, text="عينات الأزرار", 
                 font=self.fonts['heading']).pack(pady=(0, 20))
        
        # أزرار مختلفة الأنماط
        buttons_frame = ttk.Frame(frame)
        buttons_frame.pack(fill="x", pady=10)
        
        if ENHANCED_MODULES_AVAILABLE:
            # أزرار محسنة
            ttk.Button(buttons_frame, text="🔵 زر أساسي", 
                      style="Primary.TButton").pack(side="left", padx=5)
            ttk.Button(buttons_frame, text="✅ زر النجاح", 
                      style="Success.TButton").pack(side="left", padx=5)
            ttk.Button(buttons_frame, text="⚠️ زر التحذير", 
                      style="Warning.TButton").pack(side="left", padx=5)
            ttk.Button(buttons_frame, text="❌ زر الخطر", 
                      style="Danger.TButton").pack(side="left", padx=5)
            ttk.Button(buttons_frame, text="⚪ زر ثانوي", 
                      style="Secondary.TButton").pack(side="left", padx=5)
        else:
            # أزرار أساسية
            ttk.Button(buttons_frame, text="زر أساسي").pack(side="left", padx=5)
            ttk.Button(buttons_frame, text="زر ثانوي").pack(side="left", padx=5)
    
    def create_forms_test(self, parent):
        """اختبار النماذج"""
        
        frame = ttk.Frame(parent, padding="20")
        frame.pack(fill="both", expand=True)
        
        ttk.Label(frame, text="نموذج اختبار", 
                 font=self.fonts['heading']).pack(pady=(0, 20))
        
        # نموذج اختبار
        form_frame = ttk.LabelFrame(frame, text="بيانات العميل", padding="15")
        form_frame.pack(fill="x", pady=10)
        
        # حقول النموذج
        fields = [
            ("الاسم:", "اسم العميل"),
            ("الهاتف:", "رقم الهاتف"),
            ("البريد الإلكتروني:", "البريد الإلكتروني"),
            ("العنوان:", "عنوان العميل")
        ]
        
        for i, (label_text, placeholder) in enumerate(fields):
            row_frame = ttk.Frame(form_frame)
            row_frame.pack(fill="x", pady=5)
            
            ttk.Label(row_frame, text=label_text, 
                     font=self.fonts['body']).pack(side="right", padx=(0, 10))
            
            entry = ttk.Entry(row_frame, 
                             style="Modern.TEntry" if ENHANCED_MODULES_AVAILABLE else None,
                             font=self.fonts['body'])
            entry.pack(side="right", fill="x", expand=True)
            entry.insert(0, placeholder)
    
    def create_tables_test(self, parent):
        """اختبار الجداول"""
        
        frame = ttk.Frame(parent, padding="20")
        frame.pack(fill="both", expand=True)
        
        ttk.Label(frame, text="جدول اختبار", 
                 font=self.fonts['heading']).pack(pady=(0, 20))
        
        # إطار الجدول
        table_frame = ttk.Frame(frame)
        table_frame.pack(fill="both", expand=True)
        
        # الجدول
        columns = ("ID", "الاسم", "الهاتف", "الحالة")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings",
                           style="Modern.Treeview" if ENHANCED_MODULES_AVAILABLE else None)
        
        # رؤوس الأعمدة
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # بيانات تجريبية
        sample_data = [
            ("1", "أحمد محمد", "0501234567", "نشط"),
            ("2", "فاطمة علي", "0507654321", "معلق"),
            ("3", "محمد أحمد", "0509876543", "مكتمل"),
            ("4", "نورا سالم", "0502468135", "ملغي"),
        ]
        
        for item in sample_data:
            tree.insert("", "end", values=item)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_icons_test(self, parent):
        """اختبار الأيقونات"""
        
        frame = ttk.Frame(parent, padding="20")
        frame.pack(fill="both", expand=True)
        
        ttk.Label(frame, text="مجموعة الأيقونات", 
                 font=self.fonts['heading']).pack(pady=(0, 20))
        
        # إطار الأيقونات
        icons_frame = ttk.Frame(frame)
        icons_frame.pack(fill="both", expand=True)
        
        # عرض عينة من الأيقونات
        icon_categories = {
            "العمليات": ['add', 'edit', 'delete', 'save', 'copy'],
            "الملفات": ['file', 'folder', 'document', 'pdf', 'word'],
            "المستخدمين": ['user', 'users', 'client', 'company', 'team'],
            "الحالة": ['success', 'error', 'warning', 'info', 'question'],
            "الأدوات": ['settings', 'search', 'filter', 'refresh', 'print']
        }
        
        row = 0
        for category, icons in icon_categories.items():
            # عنوان الفئة
            category_label = ttk.Label(icons_frame, text=category, 
                                     font=self.fonts['heading'])
            category_label.grid(row=row, column=0, columnspan=5, sticky="w", pady=(10, 5))
            row += 1
            
            # الأيقونات
            col = 0
            for icon_name in icons:
                icon_text = icon_manager.get_icon(icon_name)
                icon_label = tk.Label(icons_frame, text=f"{icon_text} {icon_name}", 
                                    font=self.fonts['body'], bg=self.colors['bg_card'],
                                    relief="solid", borderwidth=1, padx=10, pady=5)
                icon_label.grid(row=row, column=col, padx=2, pady=2, sticky="w")
                col += 1
                if col >= 5:
                    col = 0
                    row += 1
            
            if col > 0:
                row += 1

def main():
    """تشغيل تطبيق الاختبار"""
    root = tk.Tk()
    app = DesignTestApp(root)
    
    print("🚀 تم تشغيل تطبيق اختبار التصميم")
    print("📋 يمكنك الآن فحص العناصر المختلفة في التبويبات")
    
    root.mainloop()

if __name__ == "__main__":
    main()
