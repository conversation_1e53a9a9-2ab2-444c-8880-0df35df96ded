# 🏢 نظام إدارة العقود الذكي - الإصدار المحسن v2.0

نظام شامل ومحسن لإدارة العقود وتوليدها تلقائياً باستخدام قوالب Word مع واجهة رسومية حديثة وسهلة الاستخدام.

## ✨ الجديد في الإصدار 2.0

### 🎨 تصميم محسن بالكامل
- **نظام ألوان مؤسسي حديث** مع 40+ لون متناسق
- **خطوط محسنة** مع دعم أفضل للغة العربية (Cairo, Tahoma)
- **150+ أيقونة Unicode** معبرة ومنظمة
- **تأثيرات بصرية متقدمة** مع انتقالات ناعمة وظلال خفيفة
- **واجهة مستخدم حديثة** تتماشى مع معايير التصميم المؤسسي

### 🔧 تحسينات تقنية
- **أنماط ttk محسنة** لجميع العناصر
- **نظام إدارة الأيقونات** المتقدم
- **تأثيرات hover وfocus** محسنة
- **تخطيط محسن** مع مساحات بيضاء متوازنة

## 🌟 الميزات الرئيسية

### 👥 إدارة العملاء
- إضافة عملاء جدد مع بيانات مفصلة (الاسم، الهاتف، العنوان، إلخ)
- عرض قائمة العملاء في جدول منظم ومحسن
- حذف العملاء غير المرغوب فيهم
- دعم الألقاب المختلفة (السيد، السيدة، الدكتور، إلخ)
- **واجهة محسنة** مع أيقونات معبرة وألوان متناسقة

### 📄 إدارة القوالب
- رفع قوالب Word (.docx) بسهولة
- استخراج المتغيرات من القوالب تلقائياً
- عرض المتغيرات الموجودة في كل قالب مع أيقونات مناسبة
- حذف القوالب غير المستخدمة
- **تصميم بطاقات محسن** للقوالب

### ⚙️ توليد العقود الذكي
- اختيار العميل من قائمة منسدلة محسنة
- اختيار القالب المناسب مع معاينة مرئية
- ملء المتغيرات تلقائياً من بيانات العميل
- **واجهة متغيرات محسنة** مع أيقونات وتصنيف
- معاينة مباشرة للعقد قبل التوليد
- توليد العقد النهائي بصيغة Word

### 📱 توليد QR Code للفواتير (ZATCA)
- توليد رموز QR متوافقة مع معايير الزكاة والضريبة
- حفظ وتحميل إعدادات البائع
- **واجهة محسنة** لإدخال بيانات الفاتورة
- عرض QR Code مع إمكانية الحفظ

### 🗄️ إدارة قاعدة البيانات
- قاعدة بيانات SQLite محلية
- نسخ احتياطية تلقائية
- **واجهة إدارة محسنة** مع مؤشرات الحالة

## 🎨 نظام التصميم الجديد

### الألوان الأساسية
- **الأزرق المؤسسي:** `#1e3a8a` (اللون الأساسي)
- **الأزرق الفاتح:** `#3b82f6` (للتمييز)
- **الأخضر:** `#059669` (للنجاح)
- **البرتقالي:** `#d97706` (للتحذير)
- **الأحمر:** `#dc2626` (للأخطاء)

### الخطوط المحسنة
- **العربية:** Cairo, Tahoma (دعم محسن للعربية)
- **الإنجليزية:** Segoe UI (خط حديث وواضح)
- **أحجام متدرجة:** من 10px إلى 16px

### الأيقونات
- **150+ أيقونة Unicode** مصنفة حسب الاستخدام
- أيقونات للعمليات، الملفات، المستخدمين، الحالة
- مؤشرات حالة ملونة
- أيقونات أنواع الملفات

## 📁 هيكل المشروع

```
برنامج العقود/
├── main_app.py                    # الملف الرئيسي للتطبيق
├── enhanced_ui_theme.py           # نظام التصميم المحسن
├── enhanced_icons.py              # مدير الأيقونات
├── enhanced_styles.css            # ملف CSS للمرجع
├── test_enhanced_design.py        # تطبيق اختبار التصميم
├── design_analysis.md             # تحليل التصاميم
├── design_implementation_guide.md # دليل التطبيق
├── ui_improvements.py             # تحسينات الواجهة
├── styles.css                     # ملف الأنماط الأساسي
├── db/                           # مجلد قاعدة البيانات
├── templates/                    # مجلد القوالب
├── archives/                     # مجلد الأرشيف
└── design_samples/               # عينات التصاميم
```

## 🚀 كيفية التشغيل

### 1. تشغيل البرنامج الأصلي (مع التحسينات)
```bash
python main_app.py
```

### 2. تشغيل اختبار التصميم
```bash
python test_enhanced_design.py
```

### 3. المتطلبات
```bash
pip install python-docx
pip install qrcode[pil]
pip install Pillow
```

## 🔧 المتطلبات التقنية

### المكتبات الأساسية
- `tkinter` - واجهة المستخدم الرسومية
- `python-docx` - معالجة ملفات Word
- `sqlite3` - قاعدة البيانات
- `qrcode` - توليد رموز QR
- `PIL/Pillow` - معالجة الصور

### المكتبات الاختيارية
- `docx2pdf` - تحويل Word إلى PDF
- `win32com.client` - تشغيل Word مباشرة (Windows)

## 📊 اختبار التصميم

يتضمن المشروع تطبيق اختبار شامل (`test_enhanced_design.py`) مع التبويبات التالية:

1. **🎨 الألوان** - عرض جميع الألوان المستخدمة
2. **🔘 الأزرار** - اختبار أنماط الأزرار المختلفة
3. **📝 النماذج** - اختبار حقول الإدخال والنماذج
4. **📊 الجداول** - اختبار عرض البيانات
5. **🔣 الأيقونات** - عرض مجموعة الأيقونات

## 🎯 الاستخدام المتقدم

### استخدام نظام التصميم في ملفات جديدة
```python
from enhanced_ui_theme import enhanced_theme
from enhanced_icons import icon_manager

# تطبيق النظام
style = enhanced_theme.setup_ttk_styles(root)

# استخدام الألوان
bg_color = enhanced_theme.colors['bg_primary']

# إنشاء زر مع أيقونة
button = icon_manager.create_icon_button(
    parent, 'add', 'إضافة عميل', 
    command=add_client, style="Primary.TButton"
)
```

### إنشاء مؤشرات حالة
```python
# مؤشر حالة ملون
status_label = icon_manager.create_status_indicator(
    parent, 'success', 'تم بنجاح'
)
```

## 📈 التحسينات المستقبلية

- [ ] نظام الوضع المظلم
- [ ] تحسين الاستجابة للشاشات المختلفة
- [ ] إضافة المزيد من التأثيرات البصرية
- [ ] تحسين الأداء
- [ ] إضافة أيقونات مخصصة SVG
- [ ] دعم اللغات المتعددة
- [ ] تصدير التصميم لتطبيقات أخرى

## 🤝 المساهمة

نرحب بالمساهمات لتحسين النظام:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. تطبيق التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- مراجعة دليل التطبيق (`design_implementation_guide.md`)
- تشغيل اختبار التصميم للتأكد من العمل الصحيح

---

**تم تطوير هذا النظام بعناية لتوفير تجربة مستخدم محسنة ومظهر مؤسسي حديث لبرنامج العقود الذكي.**
