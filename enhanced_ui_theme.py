"""
نظام التصميم المحسن لبرنامج العقود الذكي
Enhanced UI Theme System for Smart Contracts Application
"""

import tkinter as tk
from tkinter import ttk
import tkinter.font as tkFont

class EnhancedTheme:
    """فئة نظام التصميم المحسن"""
    
    def __init__(self):
        """تهيئة نظام الألوان والخطوط المحسن"""
        
        # نظام الألوان المحسن - مستوحى من التصاميم المؤسسية الحديثة
        self.colors = {
            # الألوان الأساسية
            'primary': '#1e3a8a',           # أزرق داكن مؤسسي
            'primary_light': '#3b82f6',     # أزرق فاتح
            'primary_dark': '#1e40af',      # أزرق أغمق
            'primary_hover': '#2563eb',     # أزرق للتمرير
            
            # الألوان الثانوية
            'secondary': '#64748b',         # رمادي أزرق
            'secondary_light': '#94a3b8',   # رمادي فاتح
            'accent': '#f59e0b',            # ذهبي للتمييز
            'accent_light': '#fbbf24',      # ذهبي فاتح
            
            # ألوان الخلفية
            'bg_primary': '#f8fafc',        # خلفية رئيسية
            'bg_secondary': '#ffffff',      # خلفية ثانوية
            'bg_card': '#ffffff',           # خلفية البطاقات
            'bg_hover': '#f1f5f9',         # خلفية عند التمرير
            'bg_selected': '#e0e7ff',      # خلفية المحدد
            
            # ألوان النص
            'text_primary': '#1e293b',      # نص أساسي
            'text_secondary': '#64748b',    # نص ثانوي
            'text_muted': '#94a3b8',        # نص خافت
            'text_white': '#ffffff',        # نص أبيض
            'text_inverse': '#ffffff',      # نص معكوس
            
            # ألوان الحالة
            'success': '#059669',           # أخضر للنجاح
            'success_light': '#10b981',     # أخضر فاتح
            'warning': '#d97706',           # برتقالي للتحذير
            'warning_light': '#f59e0b',     # برتقالي فاتح
            'error': '#dc2626',             # أحمر للخطأ
            'error_light': '#ef4444',       # أحمر فاتح
            'info': '#0284c7',              # أزرق للمعلومات
            'info_light': '#0ea5e9',        # أزرق فاتح
            
            # الحدود والخطوط
            'border': '#e2e8f0',            # لون الحدود
            'border_light': '#f1f5f9',      # حدود فاتحة
            'border_dark': '#cbd5e1',       # حدود داكنة
            'border_focus': '#3b82f6',      # حدود التركيز
            
            # ألوان خاصة
            'shadow': '#f1f5f9',            # ظل خفيف
            'overlay': '#64748b',           # طبقة تغطية
        }
        
        # نظام الخطوط المحسن
        self.fonts = {
            # الخطوط العربية
            'arabic_title': ('Cairo', 16, 'bold'),
            'arabic_heading': ('Cairo', 14, 'bold'),
            'arabic_body': ('Cairo', 12, 'normal'),
            'arabic_small': ('Cairo', 10, 'normal'),
            'arabic_button': ('Cairo', 11, 'bold'),
            
            # الخطوط الإنجليزية
            'english_title': ('Segoe UI', 16, 'bold'),
            'english_heading': ('Segoe UI', 14, 'bold'),
            'english_body': ('Segoe UI', 12, 'normal'),
            'english_small': ('Segoe UI', 10, 'normal'),
            'english_button': ('Segoe UI', 11, 'bold'),
            
            # خطوط مختلطة (افتراضية)
            'title': ('Cairo', 16, 'bold'),
            'heading': ('Cairo', 14, 'bold'),
            'body': ('Cairo', 12, 'normal'),
            'small': ('Cairo', 10, 'normal'),
            'button': ('Cairo', 11, 'bold'),
            'code': ('Consolas', 10, 'normal'),
        }
        
        # أحجام وأبعاد
        self.dimensions = {
            'padding_xs': 4,
            'padding_sm': 8,
            'padding_md': 12,
            'padding_lg': 16,
            'padding_xl': 24,
            
            'margin_xs': 4,
            'margin_sm': 8,
            'margin_md': 12,
            'margin_lg': 16,
            'margin_xl': 24,
            
            'border_width': 1,
            'border_radius': 8,
            'border_radius_lg': 12,
            
            'button_height': 36,
            'input_height': 32,
            'header_height': 60,
        }
        
        # إعدادات الظلال والتأثيرات
        self.effects = {
            'shadow_light': '2 2 4 #00000020',
            'shadow_medium': '4 4 8 #00000030',
            'shadow_heavy': '8 8 16 #00000040',
            'transition': '0.3s ease-in-out',
        }

    def setup_ttk_styles(self, root):
        """إعداد أنماط ttk المحسنة"""
        style = ttk.Style(root)
        
        # تكوين الأنماط الأساسية
        self._configure_button_styles(style)
        self._configure_frame_styles(style)
        self._configure_label_styles(style)
        self._configure_entry_styles(style)
        self._configure_treeview_styles(style)
        self._configure_notebook_styles(style)
        self._configure_progressbar_styles(style)
        
        return style
    
    def _configure_button_styles(self, style):
        """تكوين أنماط الأزرار"""
        
        # زر أساسي
        style.configure('Primary.TButton',
                       font=self.fonts['button'],
                       padding=(self.dimensions['padding_lg'], self.dimensions['padding_sm']),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_hover']),
                           ('pressed', self.colors['primary_dark']),
                           ('!active', self.colors['primary'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')])
        
        # زر النجاح
        style.configure('Success.TButton',
                       font=self.fonts['button'],
                       padding=(self.dimensions['padding_lg'], self.dimensions['padding_sm']),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Success.TButton',
                 background=[('active', self.colors['success_light']),
                           ('pressed', self.colors['success']),
                           ('!active', self.colors['success'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])])
        
        # زر التحذير
        style.configure('Warning.TButton',
                       font=self.fonts['button'],
                       padding=(self.dimensions['padding_lg'], self.dimensions['padding_sm']),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Warning.TButton',
                 background=[('active', self.colors['warning_light']),
                           ('pressed', self.colors['warning']),
                           ('!active', self.colors['warning'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])])
        
        # زر الخطر
        style.configure('Danger.TButton',
                       font=self.fonts['button'],
                       padding=(self.dimensions['padding_lg'], self.dimensions['padding_sm']),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Danger.TButton',
                 background=[('active', self.colors['error_light']),
                           ('pressed', self.colors['error']),
                           ('!active', self.colors['error'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])])
        
        # زر ثانوي
        style.configure('Secondary.TButton',
                       font=self.fonts['button'],
                       padding=(self.dimensions['padding_lg'], self.dimensions['padding_sm']),
                       relief='solid',
                       borderwidth=1,
                       focuscolor='none')
        
        style.map('Secondary.TButton',
                 background=[('active', self.colors['bg_hover']),
                           ('pressed', self.colors['border_light']),
                           ('!active', self.colors['bg_secondary'])],
                 foreground=[('active', self.colors['text_primary']),
                           ('!active', self.colors['text_primary'])],
                 bordercolor=[('active', self.colors['border_dark']),
                            ('!active', self.colors['border'])])

    def _configure_frame_styles(self, style):
        """تكوين أنماط الإطارات"""
        
        # إطار البطاقة
        style.configure('Card.TFrame',
                       background=self.colors['bg_card'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['border'])
        
        # إطار الرأس
        style.configure('Header.TFrame',
                       background=self.colors['primary'],
                       relief='flat',
                       borderwidth=0)
        
        # إطار الشريط الجانبي
        style.configure('Sidebar.TFrame',
                       background=self.colors['bg_hover'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['border'])

    def _configure_label_styles(self, style):
        """تكوين أنماط التسميات"""
        
        # عنوان رئيسي
        style.configure('Title.TLabel',
                       font=self.fonts['title'],
                       background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'])
        
        # عنوان فرعي
        style.configure('Heading.TLabel',
                       font=self.fonts['heading'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_primary'])
        
        # نص عادي
        style.configure('Body.TLabel',
                       font=self.fonts['body'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_secondary'])
        
        # نص صغير
        style.configure('Small.TLabel',
                       font=self.fonts['small'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_muted'])
        
        # تسمية النجاح
        style.configure('Success.TLabel',
                       font=self.fonts['body'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['success'])
        
        # تسمية التحذير
        style.configure('Warning.TLabel',
                       font=self.fonts['body'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['warning'])
        
        # تسمية الخطر
        style.configure('Error.TLabel',
                       font=self.fonts['body'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['error'])

    def _configure_entry_styles(self, style):
        """تكوين أنماط حقول الإدخال"""
        
        style.configure('Modern.TEntry',
                       font=self.fonts['body'],
                       padding=(self.dimensions['padding_sm'], self.dimensions['padding_xs']),
                       relief='solid',
                       borderwidth=1,
                       focuscolor='none')
        
        style.map('Modern.TEntry',
                 bordercolor=[('focus', self.colors['border_focus']),
                            ('!focus', self.colors['border'])],
                 lightcolor=[('focus', self.colors['border_focus']),
                           ('!focus', self.colors['border'])],
                 darkcolor=[('focus', self.colors['border_focus']),
                          ('!focus', self.colors['border'])])

    def _configure_treeview_styles(self, style):
        """تكوين أنماط عرض الشجرة (الجداول)"""
        
        style.configure('Modern.Treeview',
                       font=self.fonts['body'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_card'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('Modern.Treeview.Heading',
                       font=self.fonts['heading'],
                       background=self.colors['primary'],
                       foreground=self.colors['text_white'],
                       relief='flat',
                       borderwidth=1)
        
        style.map('Modern.Treeview',
                 background=[('selected', self.colors['bg_selected'])],
                 foreground=[('selected', self.colors['text_primary'])])

    def _configure_notebook_styles(self, style):
        """تكوين أنماط دفتر التبويبات"""
        
        style.configure('Modern.TNotebook',
                       background=self.colors['bg_primary'],
                       borderwidth=0)
        
        style.configure('Modern.TNotebook.Tab',
                       font=self.fonts['button'],
                       padding=(self.dimensions['padding_lg'], self.dimensions['padding_sm']),
                       background=self.colors['bg_hover'],
                       foreground=self.colors['text_secondary'])
        
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', self.colors['primary']),
                           ('active', self.colors['bg_selected']),
                           ('!selected', self.colors['bg_hover'])],
                 foreground=[('selected', self.colors['text_white']),
                           ('active', self.colors['text_primary']),
                           ('!selected', self.colors['text_secondary'])])

    def _configure_progressbar_styles(self, style):
        """تكوين أنماط شريط التقدم"""
        
        style.configure('Modern.Horizontal.TProgressbar',
                       background=self.colors['primary'],
                       troughcolor=self.colors['bg_hover'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])

    def create_gradient_frame(self, parent, **kwargs):
        """إنشاء إطار بتدرج لوني"""
        frame = tk.Frame(parent, **kwargs)
        # يمكن إضافة تدرج لوني هنا باستخدام Canvas
        return frame

    def apply_hover_effect(self, widget, hover_color=None, normal_color=None):
        """تطبيق تأثير التمرير على العنصر"""
        if hover_color is None:
            hover_color = self.colors['bg_hover']
        if normal_color is None:
            normal_color = self.colors['bg_card']
        
        def on_enter(event):
            widget.configure(background=hover_color)
        
        def on_leave(event):
            widget.configure(background=normal_color)
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

# إنشاء مثيل عام للنظام
enhanced_theme = EnhancedTheme()
