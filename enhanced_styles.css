/* نظام الألوان المحسن لبرنامج العقود الذكي */

:root {
  /* الألوان الأساسية - مستوحاة من التصاميم المؤسسية */
  --primary-color: #1e3a8a;        /* أزرق داكن مؤسسي */
  --primary-light: #3b82f6;       /* أزرق فاتح */
  --primary-dark: #1e40af;        /* أزرق أغمق */
  --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  
  /* الألوان الثانوية */
  --secondary-color: #64748b;      /* رمادي أزرق */
  --secondary-light: #94a3b8;     /* رمادي فاتح */
  --accent-color: #f59e0b;         /* ذهبي للتمييز */
  --accent-light: #fbbf24;        /* ذهبي فاتح */
  
  /* ألوان الخلفية */
  --bg-primary: #f8fafc;          /* خلفية رئيسية */
  --bg-secondary: #ffffff;        /* خلفية ثانوية */
  --bg-card: #ffffff;             /* خلفية البطاقات */
  --bg-hover: #f1f5f9;           /* خلفية عند التمرير */
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  
  /* ألوان النص */
  --text-primary: #1e293b;        /* نص أساسي */
  --text-secondary: #64748b;      /* نص ثانوي */
  --text-muted: #94a3b8;          /* نص خافت */
  --text-white: #ffffff;          /* نص أبيض */
  
  /* ألوان الحالة */
  --success: #059669;             /* أخضر للنجاح */
  --success-light: #10b981;       /* أخضر فاتح */
  --warning: #d97706;             /* برتقالي للتحذير */
  --warning-light: #f59e0b;       /* برتقالي فاتح */
  --error: #dc2626;               /* أحمر للخطأ */
  --error-light: #ef4444;         /* أحمر فاتح */
  --info: #0284c7;                /* أزرق للمعلومات */
  --info-light: #0ea5e9;          /* أزرق فاتح */
  
  /* الحدود والظلال */
  --border-color: #e2e8f0;        /* لون الحدود */
  --border-light: #f1f5f9;        /* حدود فاتحة */
  --border-dark: #cbd5e1;         /* حدود داكنة */
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* أنصاف الأقطار */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
  
  /* المساحات */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* الخطوط */
  --font-arabic: 'Cairo', 'Tahoma', 'Arial', sans-serif;
  --font-english: 'Segoe UI', 'Arial', sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  
  /* الانتقالات */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* الأنماط الأساسية */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-arabic);
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  direction: rtl;
  text-align: right;
}

/* أنماط البطاقات */
.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  border-bottom: 1px solid var(--border-light);
  padding-bottom: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* أنماط الأزرار */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-arabic);
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-fast);
  gap: var(--spacing-xs);
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: var(--primary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-white);
}

.btn-secondary:hover {
  background: var(--secondary-light);
}

.btn-success {
  background: var(--success);
  color: var(--text-white);
}

.btn-success:hover {
  background: var(--success-light);
}

.btn-warning {
  background: var(--warning);
  color: var(--text-white);
}

.btn-warning:hover {
  background: var(--warning-light);
}

.btn-error {
  background: var(--error);
  color: var(--text-white);
}

.btn-error:hover {
  background: var(--error-light);
}

/* أنماط حقول الإدخال */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-family: var(--font-arabic);
  font-size: var(--font-size-sm);
  background: var(--bg-secondary);
  color: var(--text-primary);
  transition: var(--transition-fast);
  direction: rtl;
  text-align: right;
}

.input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.input:hover {
  border-color: var(--border-dark);
}

/* أنماط الجداول */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th {
  background: var(--primary-gradient);
  color: var(--text-white);
  padding: var(--spacing-md);
  text-align: right;
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  color: var(--text-primary);
}

.table tr:nth-child(even) {
  background: var(--bg-hover);
}

.table tr:hover {
  background: var(--border-light);
}

/* أنماط التبويبات */
.tabs {
  display: flex;
  background: var(--bg-card);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.tab {
  flex: 1;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-hover);
  border: none;
  cursor: pointer;
  font-family: var(--font-arabic);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.tab.active {
  background: var(--primary-gradient);
  color: var(--text-white);
}

.tab:hover:not(.active) {
  background: var(--border-light);
  color: var(--text-primary);
}

/* أنماط الرسائل */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin: var(--spacing-md) 0;
  border-left: 4px solid;
}

.alert-success {
  background: rgba(5, 150, 105, 0.1);
  border-color: var(--success);
  color: var(--success);
}

.alert-warning {
  background: rgba(217, 119, 6, 0.1);
  border-color: var(--warning);
  color: var(--warning);
}

.alert-error {
  background: rgba(220, 38, 38, 0.1);
  border-color: var(--error);
  color: var(--error);
}

.alert-info {
  background: rgba(2, 132, 199, 0.1);
  border-color: var(--info);
  color: var(--info);
}

/* أنماط الأيقونات */
.icon {
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.icon-lg {
  width: 24px;
  height: 24px;
}

/* أنماط الشبكة */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* أنماط المساعدة */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .card {
    padding: var(--spacing-md);
  }
}
