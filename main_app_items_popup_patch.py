# --- دوال إدارة الأصناف (نافذة منبثقة) ---
def add_item_popup(self):
    def save():
        code = code_entry.get().strip()
        desc = desc_entry.get().strip()
        qty = qty_entry.get().strip()
        price = price_entry.get().strip()
        if not code or not desc or not qty or not price:
            messagebox.showwarning("تنبيه", "جميع الحقول مطلوبة!")
            return
        self.items_data.append({"code": code, "desc": desc, "qty": qty, "price": price})
        self.refresh_items_treeview()
        popup.destroy()

    popup = tk.Toplevel(self.root)
    popup.title("إضافة صنف جديد")
    popup.geometry("350x250")
    popup.transient(self.root)
    popup.grab_set()

    tk.Label(popup, text="رقم الصنف:").pack(pady=5)
    code_entry = tk.Entry(popup)
    code_entry.pack(pady=5)
    tk.Label(popup, text="وصف الصنف:").pack(pady=5)
    desc_entry = tk.Entry(popup)
    desc_entry.pack(pady=5)
    tk.Label(popup, text="الكمية:").pack(pady=5)
    qty_entry = tk.Entry(popup)
    qty_entry.pack(pady=5)
    tk.Label(popup, text="السعر:").pack(pady=5)
    price_entry = tk.Entry(popup)
    price_entry.pack(pady=5)

    btn_frame = tk.Frame(popup)
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="حفظ", command=save, bg="#28a745", fg="white").pack(side="right", padx=5)
    tk.Button(btn_frame, text="إلغاء", command=popup.destroy, bg="#dc3545", fg="white").pack(side="right")


def edit_item_popup(self):
    selected = self.items_tree.selection()
    if not selected:
        messagebox.showwarning("تنبيه", "يرجى اختيار صنف للتعديل.")
        return
    idx = self.items_tree.index(selected[0])
    item = self.items_data[idx]

    def save():
        code = code_entry.get().strip()
        desc = desc_entry.get().strip()
        qty = qty_entry.get().strip()
        price = price_entry.get().strip()
        if not code or not desc or not qty or not price:
            messagebox.showwarning("تنبيه", "جميع الحقول مطلوبة!")
            return
        self.items_data[idx] = {"code": code, "desc": desc, "qty": qty, "price": price}
        self.refresh_items_treeview()
        popup.destroy()

    popup = tk.Toplevel(self.root)
    popup.title("تعديل الصنف")
    popup.geometry("350x250")
    popup.transient(self.root)
    popup.grab_set()

    tk.Label(popup, text="رقم الصنف:").pack(pady=5)
    code_entry = tk.Entry(popup)
    code_entry.insert(0, item["code"])
    code_entry.pack(pady=5)
    tk.Label(popup, text="وصف الصنف:").pack(pady=5)
    desc_entry = tk.Entry(popup)
    desc_entry.insert(0, item["desc"])
    desc_entry.pack(pady=5)
    tk.Label(popup, text="الكمية:").pack(pady=5)
    qty_entry = tk.Entry(popup)
    qty_entry.insert(0, item["qty"])
    qty_entry.pack(pady=5)
    tk.Label(popup, text="السعر:").pack(pady=5)
    price_entry = tk.Entry(popup)
    price_entry.insert(0, item["price"])
    price_entry.pack(pady=5)

    btn_frame = tk.Frame(popup)
    btn_frame.pack(pady=10)
    tk.Button(btn_frame, text="حفظ", command=save, bg="#007bff", fg="white").pack(side="right", padx=5)
    tk.Button(btn_frame, text="إلغاء", command=popup.destroy, bg="#dc3545", fg="white").pack(side="right")


def delete_selected_item(self):
    selected = self.items_tree.selection()
    if not selected:
        messagebox.showwarning("تنبيه", "يرجى اختيار صنف للحذف.")
        return
    idx = self.items_tree.index(selected[0])
    del self.items_data[idx]
    self.refresh_items_treeview()


def refresh_items_treeview(self):
    for i in self.items_tree.get_children():
        self.items_tree.delete(i)
    for idx, item in enumerate(self.items_data, 1):
        self.items_tree.insert("", "end", values=(item["code"], item["desc"], item["qty"], item["price"]))
