#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد QR Code للزكاة والضريبة والجمارك (ZATCA)
ZATCA QR Code Generator
"""

import qrcode
from PIL import Image
import base64
import struct
from datetime import datetime
import re

def validate_and_generate_qr(seller_name, vat_number, timestamp, total_amount, vat_amount):
    """
    توليد QR Code متوافق مع معايير الزكاة والضريبة والجمارك
    
    Args:
        seller_name (str): اسم البائع
        vat_number (str): الرقم الضريبي
        timestamp (str): الطابع الزمني
        total_amount (float): المبلغ الإجمالي
        vat_amount (float): مبلغ الضريبة
    
    Returns:
        PIL.Image: صورة QR Code
    """
    try:
        # تنظيف وتحويل البيانات
        seller_name = str(seller_name).strip()
        vat_number = str(vat_number).strip()
        
        # تحويل المبالغ إلى float
        total_amount = float(total_amount) if total_amount else 0.0
        vat_amount = float(vat_amount) if vat_amount else 0.0
        
        # تنسيق الطابع الزمني
        if not timestamp:
            timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")
        
        # إنشاء البيانات المطلوبة لـ QR Code
        qr_data = create_zatca_qr_data(
            seller_name=seller_name,
            vat_number=vat_number,
            timestamp=timestamp,
            total_amount=total_amount,
            vat_amount=vat_amount
        )
        
        # إنشاء QR Code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        qr.add_data(qr_data)
        qr.make(fit=True)
        
        # إنشاء الصورة
        qr_image = qr.make_image(fill_color="black", back_color="white")
        
        return qr_image
        
    except Exception as e:
        print(f"خطأ في توليد QR Code: {e}")
        # إرجاع QR Code بسيط في حالة الخطأ
        return create_simple_qr(f"خطأ في البيانات: {e}")

def create_zatca_qr_data(seller_name, vat_number, timestamp, total_amount, vat_amount):
    """
    إنشاء البيانات المطلوبة لـ QR Code حسب معايير ZATCA
    """
    try:
        # تنسيق البيانات حسب معايير ZATCA
        # Tag 1: اسم البائع
        tag1 = create_tlv(1, seller_name)
        
        # Tag 2: الرقم الضريبي
        tag2 = create_tlv(2, vat_number)
        
        # Tag 3: الطابع الزمني
        tag3 = create_tlv(3, timestamp)
        
        # Tag 4: المبلغ الإجمالي
        tag4 = create_tlv(4, f"{total_amount:.2f}")
        
        # Tag 5: مبلغ الضريبة
        tag5 = create_tlv(5, f"{vat_amount:.2f}")
        
        # دمج جميع العلامات
        qr_data = tag1 + tag2 + tag3 + tag4 + tag5
        
        # تحويل إلى Base64
        qr_data_b64 = base64.b64encode(qr_data).decode('utf-8')
        
        return qr_data_b64
        
    except Exception as e:
        print(f"خطأ في إنشاء بيانات ZATCA: {e}")
        # إرجاع بيانات بسيطة في حالة الخطأ
        return f"البائع: {seller_name}\nالرقم الضريبي: {vat_number}\nالإجمالي: {total_amount}\nالضريبة: {vat_amount}"

def create_tlv(tag, value):
    """
    إنشاء TLV (Tag-Length-Value) للبيانات
    """
    try:
        value_bytes = value.encode('utf-8')
        length = len(value_bytes)
        
        # تحويل Tag و Length إلى bytes
        tag_byte = struct.pack('B', tag)
        length_byte = struct.pack('B', length)
        
        return tag_byte + length_byte + value_bytes
        
    except Exception as e:
        print(f"خطأ في إنشاء TLV: {e}")
        return b''

def create_simple_qr(text):
    """
    إنشاء QR Code بسيط للنص
    """
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        qr.add_data(text)
        qr.make(fit=True)
        
        qr_image = qr.make_image(fill_color="black", back_color="white")
        return qr_image
        
    except Exception as e:
        print(f"خطأ في إنشاء QR Code البسيط: {e}")
        # إنشاء صورة فارغة في حالة الخطأ الشديد
        return Image.new('RGB', (200, 200), color='white')

def extract_vat_number(text):
    """
    استخراج الرقم الضريبي من النص
    """
    if not text:
        return ""
    
    # البحث عن أرقام (الرقم الضريبي عادة 15 رقم)
    numbers = re.findall(r'\d+', str(text))
    
    # البحث عن رقم من 10-15 خانة
    for num in numbers:
        if 10 <= len(num) <= 15:
            return num
    
    # إذا لم يوجد، إرجاع أول رقم أو نص فارغ
    return numbers[0] if numbers else ""

def format_currency(amount):
    """
    تنسيق المبلغ المالي
    """
    try:
        return f"{float(amount):.2f}"
    except:
        return "0.00"

# اختبار الوحدة
if __name__ == "__main__":
    # اختبار بسيط
    test_qr = validate_and_generate_qr(
        seller_name="شركة اختبار",
        vat_number="123456789012345",
        timestamp="2024-01-01T12:00:00Z",
        total_amount=1000.00,
        vat_amount=150.00
    )
    
    if test_qr:
        print("✅ تم إنشاء QR Code بنجاح")
        # test_qr.save("test_qr.png")  # حفظ للاختبار
    else:
        print("❌ فشل في إنشاء QR Code")