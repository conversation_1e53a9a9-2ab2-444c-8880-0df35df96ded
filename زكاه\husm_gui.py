import subprocess
import sys
import importlib

# التحقق من وجود المكتبات وتثبيتها إذا لزم الأمر
required_packages = {
    'qrcode': 'qrcode[pil]',
    'PIL': 'Pillow'
}

def install_missing_packages():
    for module_name, package_name in required_packages.items():
        try:
            importlib.import_module(module_name)
        except ImportError:
            print(f"جاري تثبيت الحزمة: {package_name} ...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", package_name])

install_missing_packages()

# استيراد المكتبات بعد التحقق
import tkinter as tk
from tkinter import messagebox
from PIL import ImageTk, Image
import qrcode
import base64
from datetime import datetime, timezone
import os

# تحويل البيانات إلى TLV
def to_tlv(tag: int, value: str) -> bytes:
    if not value:
        return b''
    value_bytes = value.encode('utf-8')
    return bytes([tag]) + bytes([len(value_bytes)]) + value_bytes

# توليد رمز QR
def generate_qr():
    seller_name = entry_name.get()
    vat_number = entry_vat.get()
    invoice_total = entry_total.get()
    vat_total = entry_vat_total.get()

    if not vat_number or len(vat_number) != 15 or not vat_number.isdigit():
        messagebox.showerror("خطأ", "الرقم الضريبي يجب أن يتكون من 15 رقمًا.")
        return

    invoice_timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    tlv_bytes = b''
    tlv_bytes += to_tlv(1, seller_name)
    tlv_bytes += to_tlv(2, vat_number)
    tlv_bytes += to_tlv(3, invoice_timestamp)
    tlv_bytes += to_tlv(4, invoice_total)
    tlv_bytes += to_tlv(5, vat_total)

    base64_data = base64.b64encode(tlv_bytes).decode('utf-8')

    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_M,
        box_size=10,
        border=4,
    )
    qr.add_data(base64_data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")

    # حفظ الصورة
    filename = f"ZATCA_QR_{seller_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
    filepath = os.path.join(os.getcwd(), filename)
    img.save(filepath)

    # عرض الصورة
    img_tk = ImageTk.PhotoImage(img.resize((200, 200)))
    qr_label.configure(image=img_tk)
    qr_label.image = img_tk

    # عرض نتيجة الحفظ والبيانات
    label_result.config(text=f"تم حفظ رمز QR في:\n{filepath}")
    text_base64.delete("1.0", tk.END)
    text_base64.insert(tk.END, base64_data)

# واجهة المستخدم
root = tk.Tk()
root.title("حُسْم - مولد رمز QR للفاتورة الإلكترونية")
root.geometry("500x600")
root.resizable(False, False)
root.option_add('*font', 'Tahoma 12')

tk.Label(root, text="اسم البائع:").pack(pady=(10, 0))
entry_name = tk.Entry(root, justify='right')
entry_name.pack(fill="x", padx=20)

tk.Label(root, text="الرقم الضريبي (15 رقمًا):").pack(pady=(10, 0))
entry_vat = tk.Entry(root, justify='right')
entry_vat.pack(fill="x", padx=20)

tk.Label(root, text="إجمالي الفاتورة (شامل الضريبة):").pack(pady=(10, 0))
entry_total = tk.Entry(root, justify='right')
entry_total.pack(fill="x", padx=20)

tk.Label(root, text="قيمة الضريبة:").pack(pady=(10, 0))
entry_vat_total = tk.Entry(root, justify='right')
entry_vat_total.pack(fill="x", padx=20)

tk.Button(root, text="إنشاء رمز QR", command=generate_qr).pack(pady=20)

qr_label = tk.Label(root)
qr_label.pack(pady=10)

label_result = tk.Label(root, text="", wraplength=480, justify='center', fg='green')
label_result.pack(pady=5)

tk.Label(root, text="بيانات TLV بصيغة Base64:").pack(pady=(10, 0))
text_base64 = tk.Text(root, height=4, wrap='word', font=("Courier", 10), bg="#f0f0f0")
text_base64.pack(fill="both", expand=False, padx=20, pady=5)

root.mainloop()
