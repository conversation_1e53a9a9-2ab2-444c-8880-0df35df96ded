@echo off
chcp 65001 >nul
echo ========================================
echo      نظام إدارة العقود الذكي
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تشغيل install_requirements.bat أولاً
    pause
    exit /b 1
)

REM التحقق من وجود الملف الرئيسي
if not exist start_app.py (
    echo خطأ: ملف start_app.py غير موجود
    pause
    exit /b 1
)

echo بدء تشغيل التطبيق...
echo.

python start_app.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل التطبيق
    echo تحقق من ملف error.log للحصول على تفاصيل الخطأ
)

echo.
echo تم إغلاق التطبيق
pause