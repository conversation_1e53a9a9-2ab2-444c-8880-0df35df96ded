import tkinter as tk
from tkinter import ttk

# Function to toggle between light and dark mode
def toggle_theme():
    if root.tk.call("ttk::style", "theme", "use") == "default":
        style.theme_use("dark")
    else:
        style.theme_use("default")

# Create the main window
root = tk.Tk()
root.title("برنامج العقود")
root.geometry("800x600")

# Center the window on screen
def center_window():
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

center_window()

# Create a style object
style = ttk.Style(root)

# Define light and dark themes with RTL support
style.theme_create("default", parent="alt", settings={
    ".": {
        "configure": {
            "background": "#ffffff",
            "foreground": "#000000",
            "font": ("Arial", 12),
            "anchor": "center"
        }
    },
    "TLabel": {
        "configure": {
            "anchor": "center",
            "justify": "center"
        }
    },
    "TButton": {
        "configure": {
            "anchor": "center",
            "justify": "center"
        }
    }
})

style.theme_create("dark", parent="alt", settings={
    ".": {
        "configure": {
            "background": "#121212",
            "foreground": "#ffffff",
            "font": ("Arial", 12),
            "anchor": "center"
        }
    },
    "TLabel": {
        "configure": {
            "anchor": "center",
            "justify": "center"
        }
    },
    "TButton": {
        "configure": {
            "anchor": "center",
            "justify": "center"
        }
    }
})

# Set the default theme
style.theme_use("default")

# Create a header with RTL alignment
header = ttk.Label(root, text="مرحبًا بك في برنامج العقود", anchor="center", justify="center")
header.pack(fill=tk.X, pady=10)

# Create a button to toggle themes - centered
toggle_button = ttk.Button(root, text="تبديل الوضع", command=toggle_theme)
toggle_button.pack(pady=20)

# Add a menu bar with RTL support
menu_bar = tk.Menu(root)

# File menu
file_menu = tk.Menu(menu_bar, tearoff=0)
file_menu.add_command(label="فتح")
file_menu.add_command(label="حفظ")
file_menu.add_separator()
file_menu.add_command(label="خروج", command=root.quit)
menu_bar.add_cascade(label="ملف", menu=file_menu)

# Help menu
help_menu = tk.Menu(menu_bar, tearoff=0)
help_menu.add_command(label="عن البرنامج")
menu_bar.add_cascade(label="مساعدة", menu=help_menu)

# Configure the menu bar
root.config(menu=menu_bar)

# Add a section for data display with RTL support
data_frame = ttk.Frame(root, padding="10")
data_frame.pack(fill=tk.BOTH, expand=True, pady=10)

data_label = ttk.Label(data_frame, text="عرض البيانات هنا:", anchor="e", justify="right")
data_label.pack(anchor="e", fill=tk.X)

# Text widget with RTL support
data_text = tk.Text(data_frame, height=15, wrap=tk.WORD)
data_text.pack(fill=tk.BOTH, expand=True)
# Configure text direction for Arabic
data_text.config(justify="right")

# Add additional buttons - centered
button_frame = ttk.Frame(root, padding="10")
button_frame.pack(fill=tk.X, pady=10)

# Center the buttons using a sub-frame
center_button_frame = ttk.Frame(button_frame)
center_button_frame.pack(anchor="center")

save_button = ttk.Button(center_button_frame, text="حفظ البيانات")
save_button.pack(side=tk.RIGHT, padx=5)

load_button = ttk.Button(center_button_frame, text="تحميل العقود")
load_button.pack(side=tk.RIGHT, padx=5)

export_button = ttk.Button(center_button_frame, text="تصدير العقود")
export_button.pack(side=tk.RIGHT, padx=5)

# Run the application
root.mainloop()
