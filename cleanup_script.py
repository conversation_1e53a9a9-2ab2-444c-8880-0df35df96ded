import re
import os
import sys

# Define a dummy default_api for the script to run in the shell
class DummyDefaultApi:
    def read_file(self, absolute_path):
        with open(absolute_path, 'r', encoding='utf-8') as f:
            return {'read_file_response': {'output': f.read()}}

    def write_file(self, file_path, content):
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

default_api = DummyDefaultApi()

file_path = "C:/Users/<USER>/Desktop/smart_contracts_system/برنامج العقود/main_app.py"

full_content = default_api.read_file(absolute_path=file_path)['read_file_response']['output']
lines = full_content.splitlines()

method_definitions = []
current_method_start = -1
current_method_name = ""

for i, line in enumerate(lines):
    method_match = re.match(r"^\s*def\s+(\w+)\(self\):", line)
    if method_match:
        if current_method_start != -1:
            method_definitions.append({
                "name": current_method_name,
                "start": current_method_start,
                "end": i - 1
            })
        current_method_name = method_match.group(1)
        current_method_start = i

if current_method_start != -1:
    method_definitions.append({
        "name": current_method_name,
        "start": current_method_start,
        "end": len(lines) - 1
    })

contracts_tabs = [m for m in method_definitions if m["name"] == "setup_contracts_tab"]
qr_tabs = [m for m in method_definitions if m["name"] == "setup_qr_generator_tab"]

methods_to_keep = []
if contracts_tabs:
    methods_to_keep.append(contracts_tabs[0])
if qr_tabs:
    methods_to_keep.append(qr_tabs[0])

methods_to_keep.sort(key=lambda x: x["start"])

cleaned_lines = []
last_kept_line = -1

for method in methods_to_keep:
    if method["start"] > last_kept_line + 1:
        cleaned_lines.extend(lines[last_kept_line + 1 : method["start"]])
    
    cleaned_lines.extend(lines[method["start"] : method["end"] + 1])
    last_kept_line = method["end"]

if last_kept_line < len(lines) - 1:
    cleaned_lines.extend(lines[last_kept_line + 1:])

cleaned_content = "\n".join(cleaned_lines)

default_api.write_file(file_path=file_path, content=cleaned_content)
