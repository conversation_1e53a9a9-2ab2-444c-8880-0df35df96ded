# ZATCA E-Invoicing QR Code Generator (Interactive Version)
# هذا الكود يقوم بإنشاء رمز QR متوافق مع متطلبات الفوترة الإلكترونية
# لهيئة الزكاة والضريبة والجمارك ويطلب من المستخدم إدخال البيانات.

import qrcode
import base64
from datetime import datetime, timezone
import os

def to_tlv(tag: int, value: str) -> bytes:
    """
    يقوم بتحويل البيانات إلى تنسيق TLV (Tag-Length-Value).
    """
    if value is None or value == '':
        return b''
    value_bytes = value.encode('utf-8')
    return bytes([tag]) + bytes([len(value_bytes)]) + value_bytes

def validate_and_generate_qr(
    seller_name: str,
    vat_number: str,
    invoice_total: str,
    vat_total: str
) -> (str, object):
    """
    يتحقق من صحة البيانات وينشئ رمز QR لفاتورة إلكترونية مبسطة.
    """
    # --- التحقق من صحة الرقم الضريبي ---
    if not vat_number or len(vat_number) != 15 or not vat_number.isdigit():
        print(f"\nخطأ: الرقم الضريبي '{vat_number}' غير صالح. يجب أن يتكون من 15 رقمًا.")
        return None, None

    # --- إنشاء تاريخ ووقت الفاتورة بالصيغة المطلوبة ---
    invoice_timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    print(f"الوقت والتاريخ المستخدم في الرمز: {invoice_timestamp}")

    # --- بناء حقول TLV ---
    tlv_bytes = b''
    tlv_bytes += to_tlv(1, seller_name)
    tlv_bytes += to_tlv(2, vat_number)
    tlv_bytes += to_tlv(3, invoice_timestamp)
    tlv_bytes += to_tlv(4, invoice_total)
    tlv_bytes += to_tlv(5, vat_total)

    # --- تحويل بيانات TLV إلى Base64 ---
    base64_tlv = base64.b64encode(tlv_bytes).decode('utf-8')

    # --- إنشاء رمز الاستجابة السريعة (QR Code) ---
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_M,
        box_size=10,
        border=4,
    )
    qr.add_data(base64_tlv)
    qr.make(fit=True)
    img = qr.make_image(fill_color='black', back_color='white')

    return base64_tlv, img

def main():
    """
    الدالة الرئيسية لتشغيل البرنامج التفاعلي.
    """
    print("--- برنامج إنشاء رمز الاستجابة السريعة للفاتورة الإلكترونية (ZATCA) ---")
    print("الرجاء إدخال بيانات الفاتورة:")

    try:
        while True:
            seller_name = input("- اسم البائع: ")
            vat_number = input("- الرقم الضريبي للبائع (15 رقمًا): ")
            invoice_total = input("- إجمالي الفاتورة (شامل الضريبة): ")
            vat_total = input("- إجمالي ضريبة القيمة المضافة: ")

            base64_qr_data, qr_image = validate_and_generate_qr(
                seller_name, vat_number, invoice_total, vat_total
            )

            if qr_image and base64_qr_data:
                # إنشاء اسم ملف فريد
                filename = f"ZATCA_QR_{seller_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
                qr_image.save(filename)
                print(f"\n✅ تم إنشاء رمز QR بنجاح وحفظه في الملف: {os.path.abspath(filename)}")
                print("\nبيانات Base64 TLV المضمنة:")
                print(base64_qr_data)
            else:
                print("\n❌ فشل إنشاء الرمز. يرجى مراجعة البيانات المدخلة.")

            another = input("\nهل تريد إنشاء رمز QR آخر؟ (نعم/لا): ").strip().lower()
            if another not in ['نعم', 'yes', 'y']:
                break
    except KeyboardInterrupt:
        print("\n\nتم الخروج من البرنامج. شكرًا لاستخدامك.")

if __name__ == "__main__":
    # --- متطلبات التشغيل ---
    # pip install qrcode[pil]
    main()
