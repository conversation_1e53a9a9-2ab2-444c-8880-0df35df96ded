#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل محسن لنظام إدارة العقود الذكي
يتضمن معالجة أفضل للأخطاء وإخفاء التحذيرات غير المهمة
"""

import warnings
import sys
import os

# إخفاء تحذيرات pkg_resources
warnings.filterwarnings("ignore", message="pkg_resources is deprecated")
warnings.filterwarnings("ignore", category=DeprecationWarning)

# التأكد من أن المسار الحالي صحيح
current_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(current_dir)

try:
    # استيراد وتشغيل التطبيق الرئيسي
    from main_app import *
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(DB_PATH):
        print("إنشاء قاعدة البيانات...")
        from create_database import create_database
        create_database()
        print("تم إنشاء قاعدة البيانات بنجاح!")
    
    # تشغيل التطبيق
    print("بدء تشغيل نظام إدارة العقود الذكي...")
    root = tk.Tk()
    app = SmartContractApp(root)
    root.mainloop()
    
except ImportError as e:
    print(f"خطأ في استيراد المكتبات المطلوبة: {e}")
    print("يرجى تشغيل install_requirements.bat أولاً")
    input("اضغط Enter للخروج...")
    
except Exception as e:
    print(f"حدث خطأ أثناء تشغيل التطبيق: {e}")
    input("اضغط Enter للخروج...")