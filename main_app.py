import warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated")
warnings.filterwarnings("ignore", category=DeprecationWarning)

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
from datetime import datetime
import os
import shutil
import json
from docxtpl import DocxTemplate, InlineImage
from docx.shared import Mm
import base64
from hijri_converter import Gregorian
import re
import subprocess # لإضافة دعم لأنظمة التشغيل الأخرى لفتح الملفات
import mammoth
from tkhtmlview import HTMLScrolledText
import tempfile
import webbrowser
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'زكاه'))
try:
    from zatca_generator import validate_and_generate_qr
    ZATCA_AVAILABLE = True
except ImportError:
    ZATCA_AVAILABLE = False
    # print("تحذير: zatca_generator غير متاح - QR Code معطل")
import io
from PIL import ImageTk, Image
try:
    from docx2pdf import convert
    DOCX2PDF_AVAILABLE = True
except ImportError:
    DOCX2PDF_AVAILABLE = False
    # print("تحذير: docx2pdf غير متاح - معاينة PDF معطلة")

try:
    import win32com.client
    WIN32COM_AVAILABLE = True
except ImportError:
    WIN32COM_AVAILABLE = False
    # print("تحذير: win32com غير متاح - معاينة Word مباشرة معطلة")

# المسارات والمجلدات
# هذا DB_PATH هنا يحدد المسار الافتراضي للاتصال بـ SQLite
DB_PATH = "db/smart_contracts.db"
TEMPLATES_DIR = "templates"
ARCHIVE_DIR = "archives"

# التأكد من وجود المجلدات الضرورية عند بدء تشغيل البرنامج
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True) # لضمان وجود مجلد db
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(ARCHIVE_DIR, exist_ok=True)

class SmartContractApp:
    def is_maintenance_or_installation_contract(self, template_name):
        """التحقق إذا كان القالب عقد صيانة أو تركيب وتوريد"""
        if not template_name:
            return False
        template_lower = template_name.lower()
        keywords = ["صيانة", "تركيب", "توريد"]
        return any(k in template_lower for k in keywords)
    def get_next_agreement_number(self):
        """إرجاع رقم الاتفاقية التالي (متسلسل يبدأ من 1001)"""
        try:
            self.cursor.execute("SELECT MAX(agreement_number) FROM contracts")
            result = self.cursor.fetchone()
            if result and result[0]:
                return result[0] + 1
            else:
                return 1001
        except Exception as e:
            return 1001
    # --- دوال إدارة الأصناف (لوحة جانبية) ---
    def add_item_side_panel(self):
        """إظهار اللوحة الجانبية لإضافة صنف جديد"""
        self.current_editing_item_idx = None  # تعيين حالة الإضافة
        self.clear_item_form()  # مسح الحقول
        self.update_item_form_title("➕ إضافة صنف جديد")  # تحديث العنوان
        self.show_item_form()  # إظهار اللوحة الجانبية

    def edit_item_side_panel(self):
        """إظهار اللوحة الجانبية لتعديل صنف موجود"""
        selected = self.items_tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "يرجى اختيار صنف للتعديل.")
            return
        
        idx = self.items_tree.index(selected[0])
        item = self.items_data[idx]
        
        # تعيين حالة التعديل
        self.current_editing_item_idx = idx
        
        # ملء الحقول ببيانات الصنف المحدد
        self.item_form_entries["code"].delete(0, tk.END)
        self.item_form_entries["code"].insert(0, item["code"])
        
        self.item_form_entries["desc"].delete(0, tk.END)
        self.item_form_entries["desc"].insert(0, item["desc"])
        
        self.item_form_entries["qty"].delete(0, tk.END)
        self.item_form_entries["qty"].insert(0, item["qty"])
        
        self.item_form_entries["price"].delete(0, tk.END)
        self.item_form_entries["price"].insert(0, item["price"])
        
        # تحديث العنوان وإظهار اللوحة
        self.update_item_form_title("✏️ تعديل الصنف")
        self.show_item_form()

    # الدوال القديمة للنوافذ المنبثقة (محتفظ بها للتوافق مع الإصدارات السابقة)
    def add_item_popup(self):
        """استخدام اللوحة الجانبية بدلاً من النافذة المنبثقة"""
        self.add_item_side_panel()

    def edit_item_popup(self):
        """استخدام اللوحة الجانبية بدلاً من النافذة المنبثقة"""
        self.edit_item_side_panel()

    def delete_selected_item(self):
        selected = self.items_tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "يرجى اختيار صنف للحذف.")
            return
        idx = self.items_tree.index(selected[0])
        del self.items_data[idx]
        self.refresh_items_treeview()

    def refresh_items_treeview(self):
        for i in self.items_tree.get_children():
            self.items_tree.delete(i)
        for idx, item in enumerate(self.items_data, 1):
            self.items_tree.insert("", "end", values=(item["code"], item["desc"], item["qty"], item["price"]))
        
        # تحديث حقل الإجمالي تلقائياً من جدول الأصناف
        self.update_total_from_items()
    
    def update_total_from_items(self):
        """تحديث حقل الإجمالي في بيانات العقد من جدول الأصناف"""
        if not hasattr(self, 'variable_entries') or not self.variable_entries:
            return
            
        # حساب الإجمالي من جدول الأصناف
        items_total = self.calculate_total_items_price()
        
        # البحث عن حقل الإجمالي
        total_field = self.find_total_field()
        if not total_field:
            # البحث عن حقول بديلة إذا لم يوجد حقل الإجمالي الرئيسي
            total_field = self.find_subtotal_field()
        
        if total_field and total_field in self.variable_entries:
            # تحديث حقل total (الإجمالي الفرعي)
            total_entry = self.variable_entries[total_field]
            total_entry.delete(0, tk.END)
            if items_total > 0:
                total_entry.insert(0, self.format_currency(items_total))
            
            # حساب وتحديث الضريبة (15%)
            tax_amount = items_total * 0.15
            tax_field = self.find_tax_field()
            if tax_field and tax_field in self.variable_entries:
                tax_entry = self.variable_entries[tax_field]
                tax_entry.delete(0, tk.END)
                if tax_amount > 0:
                    tax_entry.insert(0, self.format_currency(tax_amount))
            
            # حساب وتحديث الإجمالي النهائي (total + ضريبة)
            final_total = items_total + tax_amount
            final_total_field = self.find_final_total_field()
            if final_total_field and final_total_field in self.variable_entries:
                final_total_entry = self.variable_entries[final_total_field]
                final_total_entry.delete(0, tk.END)
                if final_total > 0:
                    final_total_entry.insert(0, self.format_currency(final_total))
            
            # تحديث عرض QR إذا كان متاحاً
            if hasattr(self, 'update_qr_display'):
                self.update_qr_display()

            #  تحديث حقول الزكاة تلقائياً
            self.update_zatca_fields_from_contract()
    
    def is_invoice_template(self, template_name):
        """التحقق من كون القالب فاتورة"""
        if not template_name:
            return False
        template_lower = template_name.lower()
        invoice_keywords = ['فاتورة', 'invoice', 'bill', 'receipt', 'فاتوره', 'فواتير']
        return any(keyword in template_lower for keyword in invoice_keywords)
    
    def generate_zatca_qr(self, total_amount, vat_amount):
        """توليد رمز QR للزكاة"""
        if not ZATCA_AVAILABLE:
            return None
            
        try:
            seller_name = self.zatca_settings.get('seller_name', 'اسم الشركة')
            vat_number = self.zatca_settings.get('vat_number', '300000000000003')
            
            qr_base64, qr_image = validate_and_generate_qr(
                seller_name, vat_number, str(total_amount), str(vat_amount)
            )
            
            if qr_image:
                # حفظ الصورة في ملف مؤقت
                qr_temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
                qr_image.save(qr_temp_file.name)
                qr_temp_file.close()
                self.temp_files_to_cleanup.append(qr_temp_file.name)
                return qr_temp_file.name
        except Exception as e:
            print(f"خطأ في توليد QR: {e}")
        
        return None
    
    

    def add_zatca_fields(self):
        """إضافة حقول إعدادات الزكاة للفواتير"""
        pass
    
    def save_zatca_settings(self, event=None):
        """حفظ إعدادات الزكاة"""
        if hasattr(self, 'seller_name_entry') and hasattr(self, 'vat_number_entry'):
            self.zatca_settings['seller_name'] = self.seller_name_entry.get()
            self.zatca_settings['vat_number'] = self.vat_number_entry.get()

    def __init__(self, root):
        self.root = root
        self.root.title("🏢 نظام إدارة العقود الذكي - Smart Contracts System v2.0")
        self.root.geometry("1200x750")
        # سيتم تحديث لون الخلفية بعد إعداد الألوان
        self.root.configure(bg="#f8fafc")
        
        # تحسين النافذة
        self.root.minsize(1000, 600)  # حد أدنى للحجم
        
        # توسيط النافذة على الشاشة
        self.center_window()
        
        # تحسين الخطوط والألوان
        self.setup_styles()

        # تحديث لون خلفية النافذة الرئيسية بعد إعداد الألوان
        self.root.configure(bg=self.colors['bg_main'])

        self.db_conn = None
        self.cursor = None
        self.connect_db()

        self.current_client_data = {}
        self.template_files = {} # لتخزين مسار الملف لكل قالب
        self.variable_entries = {} # لتخزين حقول الإدخال للمتغيرات الديناميكية
        self.preview_timer = None # لغرض debouncing (إذا أردت تطبيقه لاحقًا)

        # قاموس: هل القالب يدعم جدول أصناف؟
        self.template_has_items_table = {}

        # تهيئة template_combo هنا لضمان وجودها قبل أي استدعاءات قد تحاول الوصول إليها
        self.template_combo = None # <--- إضافة هذا السطر الجديد
        
        # متغيرات إدارة الأصناف
        self.current_editing_item_idx = None  # لتتبع حالة التعديل (None = إضافة، رقم = تعديل)
        
        # متغيرات نافذة المعاينة
        self.preview_window = None
        self.preview_text = None
        self.temp_files_to_cleanup = []  # قائمة الملفات المؤقتة للتنظيف
        
        # إعدادات الزكاة الافتراضية
        self.zatca_settings = {
            'seller_name': 'اسم الشركة',
            'vat_number': '300000000000003'
        }

        self.setup_tabs()
        self.setup_status_bar()
        
        # ربط حدث إغلاق النافذة بتنظيف الملفات المؤقتة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def on_closing(self):
        """تنظيف الموارد قبل إغلاق التطبيق"""
        # إغلاق اتصال قاعدة البيانات
        if self.db_conn:
            self.db_conn.close()
        
        # حذف الملفات المؤقتة
        for temp_file in self.temp_files_to_cleanup:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except OSError as e:
                # تجاهل الأخطاء التي تحدث إذا كان الملف قيد الاستخدام
                pass
        
        # حذف صورة QR Code النهائية إذا كانت موجودة في أي مجلد متوقع
        try:
            import os
            qr_paths = [
                os.path.join(os.getcwd(), 'zatca_qr.png'),
                os.path.join(os.getcwd(), 'زكاه', 'zatca_qr.png')
            ]
            for qr_file in qr_paths:
                if os.path.exists(qr_file):
                    try:
                        os.remove(qr_file)
                    except Exception:
                        pass
        except Exception:
            pass
        # إغلاق النافذة الرئيسية
        self.root.destroy()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_styles(self):
        """إعداد الأنماط والخطوط والألوان المحسنة"""
        # إعداد الخطوط المحسنة - مع دعم أفضل للعربية
        self.fonts = {
            'title': ('Cairo', 16, 'bold'),
            'heading': ('Cairo', 14, 'bold'),
            'body': ('Cairo', 12, 'normal'),
            'button': ('Cairo', 11, 'bold'),
            'small': ('Cairo', 10, 'normal'),
            'arabic': ('Cairo', 12, 'normal'),
            'arabic_title': ('Cairo', 16, 'bold'),
            'arabic_heading': ('Cairo', 14, 'bold'),
            'english': ('Segoe UI', 12, 'normal'),
            'code': ('Consolas', 10, 'normal')
        }

        # إعداد الألوان المحسنة - نظام ألوان مؤسسي حديث
        self.colors = {
            # الألوان الأساسية
            'primary': '#1e3a8a',           # أزرق داكن مؤسسي
            'primary_light': '#3b82f6',     # أزرق فاتح
            'primary_dark': '#1e40af',      # أزرق أغمق
            'primary_hover': '#2563eb',     # أزرق للتمرير

            # الألوان الثانوية
            'secondary': '#64748b',         # رمادي أزرق
            'secondary_light': '#94a3b8',   # رمادي فاتح
            'accent': '#f59e0b',            # ذهبي للتمييز
            'accent_light': '#fbbf24',      # ذهبي فاتح

            # ألوان الخلفية
            'bg_main': '#f8fafc',           # خلفية رئيسية
            'bg_card': '#ffffff',           # خلفية البطاقات
            'bg_hover': '#f1f5f9',         # خلفية عند التمرير
            'bg_selected': '#e0e7ff',      # خلفية المحدد
            'light': '#f8fafc',            # خلفية فاتحة
            'white': '#ffffff',            # أبيض

            # ألوان النص
            'text_primary': '#1e293b',      # نص أساسي
            'text_secondary': '#64748b',    # نص ثانوي
            'text_muted': '#94a3b8',        # نص خافت
            'text_white': '#ffffff',        # نص أبيض

            # ألوان الحالة
            'success': '#059669',           # أخضر للنجاح
            'success_light': '#10b981',     # أخضر فاتح
            'danger': '#dc2626',            # أحمر للخطأ
            'error': '#dc2626',             # أحمر للخطأ (مرادف)
            'error_light': '#ef4444',       # أحمر فاتح
            'warning': '#d97706',           # برتقالي للتحذير
            'warning_light': '#f59e0b',     # برتقالي فاتح
            'info': '#0284c7',              # أزرق للمعلومات
            'info_light': '#0ea5e9',        # أزرق فاتح

            # الحدود والخطوط
            'border': '#e2e8f0',            # لون الحدود
            'border_light': '#f1f5f9',      # حدود فاتحة
            'border_dark': '#cbd5e1',       # حدود داكنة
            'border_focus': '#3b82f6',      # حدود التركيز

            # ألوان قديمة للتوافق
            'dark': '#1e293b',
        }
        
        # إعداد أنماط ttk المحسنة
        style = ttk.Style()

        # تحسين أنماط الأزرار مع التصميم الجديد
        style.configure('Primary.TButton',
                       font=self.fonts['button'],
                       padding=(16, 10),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_hover']),
                           ('pressed', self.colors['primary_dark']),
                           ('!active', self.colors['primary'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        style.configure('Success.TButton',
                       font=self.fonts['button'],
                       padding=(16, 10),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        style.map('Success.TButton',
                 background=[('active', self.colors['success_light']),
                           ('pressed', self.colors['success']),
                           ('!active', self.colors['success'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])])

        style.configure('Danger.TButton',
                       font=self.fonts['button'],
                       padding=(16, 10),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        style.map('Danger.TButton',
                 background=[('active', self.colors['error_light']),
                           ('pressed', self.colors['danger']),
                           ('!active', self.colors['danger'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])])

        style.configure('Warning.TButton',
                       font=self.fonts['button'],
                       padding=(16, 10),
                       relief='flat',
                       borderwidth=0,
                       focuscolor='none')
        style.map('Warning.TButton',
                 background=[('active', self.colors['warning_light']),
                           ('pressed', self.colors['warning']),
                           ('!active', self.colors['warning'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])])

        # تحسين أنماط الإطارات مع ظلال خفيفة
        style.configure('Card.TFrame',
                       background=self.colors['bg_card'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['border'])

        # تحسين أنماط التسميات مع الخطوط الجديدة
        style.configure('Title.TLabel',
                       font=self.fonts['title'],
                       background=self.colors['bg_main'],
                       foreground=self.colors['text_primary'])

        style.configure('Heading.TLabel',
                       font=self.fonts['heading'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_primary'])

        style.configure('Body.TLabel',
                       font=self.fonts['body'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_secondary'])

        style.configure('Small.TLabel',
                       font=self.fonts['small'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_muted'])

        # تحسين أنماط حقول الإدخال مع تأثيرات التركيز
        style.configure('Modern.TEntry',
                       font=self.fonts['body'],
                       padding=(12, 8),
                       relief='solid',
                       borderwidth=1,
                       focuscolor='none')
        style.map('Modern.TEntry',
                 bordercolor=[('focus', self.colors['border_focus']),
                            ('!focus', self.colors['border'])],
                 lightcolor=[('focus', self.colors['border_focus']),
                           ('!focus', self.colors['border'])],
                 darkcolor=[('focus', self.colors['border_focus']),
                          ('!focus', self.colors['border'])])

        # تحسين أنماط القوائم المنسدلة
        style.configure('Modern.TCombobox',
                       font=self.fonts['body'],
                       padding=(12, 8),
                       relief='solid',
                       borderwidth=1,
                       focuscolor='none')
        style.map('Modern.TCombobox',
                 bordercolor=[('focus', self.colors['border_focus']),
                            ('!focus', self.colors['border'])])

        # إضافة نمط ثانوي محسن للأزرار
        style.configure('Secondary.TButton',
                       font=self.fonts['button'],
                       padding=(16, 10),
                       relief='solid',
                       borderwidth=1,
                       focuscolor='none')
        style.map('Secondary.TButton',
                 background=[('active', self.colors['bg_hover']),
                           ('pressed', self.colors['border_light']),
                           ('!active', self.colors['bg_card'])],
                 foreground=[('active', self.colors['text_primary']),
                           ('!active', self.colors['text_primary'])],
                 bordercolor=[('active', self.colors['border_dark']),
                            ('!active', self.colors['border'])])

        # نمط محسن لبطاقات القوالب
        style.configure('TemplateCard.TButton',
                       font=self.fonts['button'],
                       padding=(16, 10),
                       relief='solid',
                       borderwidth=1,
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_primary'],
                       focuscolor='none')
        style.map('TemplateCard.TButton',
                 background=[('active', self.colors['primary']),
                           ('selected', self.colors['primary']),
                           ('!active', self.colors['bg_card'])],
                 foreground=[('active', self.colors['text_white']),
                           ('selected', self.colors['text_white']),
                           ('!active', self.colors['text_primary'])],
                 bordercolor=[('active', self.colors['primary']),
                            ('selected', self.colors['primary']),
                            ('!active', self.colors['border'])])

        # تحسين أنماط التبويبات مع التصميم الجديد
        style.configure("Modern.TNotebook",
                       background=self.colors['bg_main'],
                       tabposition='n',
                       borderwidth=0)
        style.configure("Modern.TNotebook.Tab",
                       font=self.fonts['heading'],
                       padding=[24, 14],
                       borderwidth=0,
                       focuscolor='none')
        style.map("Modern.TNotebook.Tab",
                 background=[("selected", self.colors['primary']),
                           ("active", self.colors['primary_hover']),
                           ("!selected", self.colors['bg_hover'])],
                 foreground=[("selected", self.colors['text_white']),
                           ("active", self.colors['text_white']),
                           ("!selected", self.colors['text_secondary'])])

        # تحسين أنماط الجداول (Treeview)
        style.configure("Modern.Treeview",
                       font=self.fonts['body'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['bg_card'],
                       borderwidth=1,
                       relief='solid',
                       rowheight=32)

        style.configure("Modern.Treeview.Heading",
                       font=self.fonts['heading'],
                       background=self.colors['primary'],
                       foreground=self.colors['text_white'],
                       relief='flat',
                       borderwidth=1)

        style.map("Modern.Treeview",
                 background=[('selected', self.colors['bg_selected']),
                           ('!selected', self.colors['bg_card'])],
                 foreground=[('selected', self.colors['text_primary']),
                           ('!selected', self.colors['text_primary'])])

        style.map("Modern.Treeview.Heading",
                 background=[('active', self.colors['primary_hover']),
                           ('!active', self.colors['primary'])],
                 foreground=[('active', self.colors['text_white']),
                           ('!active', self.colors['text_white'])])

        # تحسين أنماط شريط التقدم
        style.configure('Modern.Horizontal.TProgressbar',
                       background=self.colors['primary'],
                       troughcolor=self.colors['bg_hover'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])

        # تحسين أنماط إطارات التسمية
        style.configure('Modern.TLabelFrame',
                       background=self.colors['bg_card'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['border'])

        style.configure('Modern.TLabelFrame.Label',
                       font=self.fonts['heading'],
                       background=self.colors['bg_card'],
                       foreground=self.colors['text_primary'])

    def connect_db(self):
        try:
            self.db_conn = sqlite3.connect(DB_PATH)
            self.cursor = self.db_conn.cursor()
            self.ensure_database_structure()
        except sqlite3.Error as e:
            messagebox.showerror("❌ خطأ في قاعدة البيانات", f"""فشل الاتصال بقاعدة البيانات:
{e}""")
            self.root.destroy() # إغلاق التطبيق إذا لم يتمكن من الاتصال بقاعدة البيانات
    
    def ensure_database_structure(self):
        """التأكد من وجود جميع الأعمدة المطلوبة في قاعدة البيانات"""
        try:
            # التحقق من وجود عمود variables في جدول templates
            self.cursor.execute("PRAGMA table_info(templates)")
            columns = [column[1] for column in self.cursor.fetchall()]
            
            if 'variables' not in columns:
                print("إضافة عمود variables إلى جدول templates...")
                self.cursor.execute("ALTER TABLE templates ADD COLUMN variables TEXT")
                self.db_conn.commit()
                print("تم إضافة عمود variables بنجاح")
                
        except sqlite3.Error as e:
            print(f"خطأ في التحقق من بنية قاعدة البيانات: {e}")

    def create_scrollable_page(self, parent):
        canvas = tk.Canvas(parent, bg=self.colors.get('bg_main', '#f8f9fa'), highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style="TFrame")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        def _on_mousewheel(event):
            # Check if the mouse is over the canvas
            if canvas.winfo_containing(event.x_root, event.y_root) == canvas:
                 canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        # Bind to the specific canvas, not all widgets
        canvas.bind("<MouseWheel>", _on_mousewheel)

        return canvas, scrollable_frame

    def setup_tabs(self):
        # تحسين أنماط التبويبات
        style = ttk.Style()
        style.configure("Modern.TNotebook", 
                       background=self.colors['bg_main'], 
                       tabposition='n',
                       borderwidth=0)
        style.configure("Modern.TNotebook.Tab", 
                       font=self.fonts['heading'], 
                       padding=[20, 12],
                       borderwidth=0,
                       focuscolor='none')
        style.map("Modern.TNotebook.Tab", 
                 background=[("selected", self.colors['primary']),
                           ("active", "#0056b3"),
                           ("!selected", self.colors['light'])],
                 foreground=[("selected", "white"), 
                           ("active", "white"),
                           ("!selected", self.colors['text_primary'])])

        # إطار رئيسي لتوسيط المحتوى
        main_container = ttk.Frame(self.root)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)

        # إنشاء التبويبات مع تحسينات بصرية ومحاذاة وسط
        self.notebook = ttk.Notebook(main_container, style="Modern.TNotebook")
        self.notebook.pack(fill="both", expand=True, anchor="center")

        # تبويب العملاء مع تحسينات بصرية
        self.clients_tab = ttk.Frame(self.notebook)
        self.clients_tab.configure(style="TFrame")
        self.notebook.add(self.clients_tab, text="👥  إدارة العملاء")
        self.setup_clients_tab()

        # تبويب القوالب مع تحسينات بصرية
        self.templates_tab = ttk.Frame(self.notebook)
        self.templates_tab.configure(style="TFrame")
        self.notebook.add(self.templates_tab, text="📄  إدارة القوالب")
        self.setup_templates_tab()

        # تبويب توليد العقود مع تحسينات بصرية
        self.contracts_tab = ttk.Frame(self.notebook)
        self.contracts_tab.configure(style="TFrame")
        self.notebook.add(self.contracts_tab, text="✨  توليد العقود")
        self.setup_contracts_tab()



        

    # --- تبويب العملاء ---
    def setup_clients_tab(self):
        # إنشاء صفحة قابلة للتمرير
        canvas, scrollable_frame = self.create_scrollable_page(self.clients_tab)

        # إطار العنوان الرئيسي مع محاذاة وسط
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))

        title_label = ttk.Label(title_frame, text="👥 إدارة العملاء", style="Title.TLabel")
        title_label.pack(anchor="center")

        # إطار البحث والأدوات مع محاذاة وسط
        tools_frame = ttk.Frame(scrollable_frame, style="Card.TFrame", padding="20")
        tools_frame.pack(fill="x", padx=40, pady=(0, 20))

        # إطار البحث مع محاذاة وسط
        search_frame = ttk.Frame(tools_frame)
        search_frame.pack(anchor="center", pady=(0, 15))

        search_label = ttk.Label(search_frame, text="🔍 البحث السريع:", style="Heading.TLabel")
        search_label.pack(side="left", padx=(0, 10))

        self.client_search_entry = ttk.Entry(search_frame, width=35, style="Modern.TEntry", font=self.fonts['body'])
        self.client_search_entry.pack(side="left")
        self.client_search_entry.bind("<KeyRelease>", self.filter_clients)
        # إضافة أحداث التمرير
        self.client_search_entry.bind("<Enter>", lambda e: self.show_element_info("حقل البحث", width="35 حرف", extra_info="البحث في قائمة العملاء"))
        self.client_search_entry.bind("<Leave>", lambda e: self.clear_hover_info())

        # إطار الأزرار مع محاذاة وسط
        buttons_frame = ttk.Frame(tools_frame)
        buttons_frame.pack(anchor="center")

        add_btn = ttk.Button(buttons_frame, text="➕ إضافة عميل جديد",
                           style="Success.TButton", command=self.add_client)
        add_btn.pack(side="left", padx=(0, 15))
        # إضافة أحداث التمرير
        add_btn.bind("<Enter>", lambda e: self.show_element_info("زر إضافة عميل", extra_info="إضافة عميل جديد للنظام"))
        add_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        edit_btn = ttk.Button(buttons_frame, text="✏️ تعديل العميل",
                            style="Primary.TButton", command=self.edit_client)
        edit_btn.pack(side="left", padx=(0, 15))
        # إضافة أحداث التمرير
        edit_btn.bind("<Enter>", lambda e: self.show_element_info("زر تعديل العميل", extra_info="تعديل بيانات العميل المحدد"))
        edit_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        delete_btn = ttk.Button(buttons_frame, text="🗑️ حذف العميل",
                              style="Danger.TButton", command=self.delete_client)
        delete_btn.pack(side="left")
        # إضافة أحداث التمرير
        delete_btn.bind("<Enter>", lambda e: self.show_element_info("زر حذف العميل", extra_info="حذف العميل المحدد نهائياً"))
        delete_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        # إطار رئيسي لتقسيم الشاشة إلى قسمين (الجدول على اليمين، النموذج على اليسار)
        main_clients_frame = ttk.Frame(scrollable_frame)
        main_clients_frame.pack(fill="both", expand=True, padx=40, pady=(20, 40), anchor="center")

        main_clients_frame.grid_columnconfigure(0, weight=1) # عمود للجدول
        main_clients_frame.grid_columnconfigure(1, weight=0) # عمود للمفاصل (عرض ثابت)
        main_clients_frame.grid_columnconfigure(2, weight=1) # عمود للنموذج
        main_clients_frame.grid_rowconfigure(0, weight=1)

        # إطار جدول العملاء مع تحسينات بصرية
        table_frame = ttk.Frame(main_clients_frame, style="Card.TFrame", padding="10")
        table_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))

        # فاصل عمودي
        separator = ttk.Separator(main_clients_frame, orient="vertical")
        separator.grid(row=0, column=1, sticky="ns", padx=5)

        # إطار نموذج إضافة/تعديل العميل
        self.client_form_panel = ttk.Frame(main_clients_frame, style="Card.TFrame", padding="15")
        self.client_form_panel.grid(row=0, column=2, sticky="nsew", padx=(5, 0))
        
        self.setup_client_form_panel() # استدعاء لإنشاء حقول النموذج
        self.hide_client_form() # إخفاء النموذج في البداية
        
        # عنوان الجدول مع محاذاة وسط
        table_title = ttk.Label(table_frame, text="📋 قائمة العملاء", style="Heading.TLabel")
        table_title.grid(row=0, column=0, sticky="ew", pady=(0, 15))
        table_title.configure(anchor="center")
        
        # إطار الجدول مع شريط التمرير
        tree_frame = ttk.Frame(table_frame)
        tree_frame.grid(row=1, column=0, sticky="nsew")
        
        # Configure table_frame to expand rows/columns
        table_frame.grid_rowconfigure(1, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview للعملاء مع تحسينات
        columns = ("ID", "اللقب", "الاسم", "الهاتف", "البريد الإلكتروني", "المدينة", "المنطقة", "الشارع", "المبنى", "الرقم الضريبي", "تاريخ الإدخال")
        self.clients_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", selectmode="browse")
        # إضافة أحداث التمرير للجدول
        self.clients_tree.bind("<Enter>", lambda e: self.show_element_info("جدول العملاء", width=f"{len(columns)} أعمدة", extra_info="قائمة جميع العملاء المسجلين"))
        self.clients_tree.bind("<Leave>", lambda e: self.clear_hover_info())
        
        # تحسين أنماط الجدول
        style = ttk.Style()
        style.configure("Treeview", font=self.fonts['button'], rowheight=30) # Changed font to button size
        style.configure("Treeview.Heading", font=self.fonts['heading'], background=self.colors['primary'], foreground="black")
        
        # تعريف الأعمدة مع تحسينات
        column_configs = {
            "ID": {"text": "🆔 الرقم", "width": 0, "minwidth": 0, "anchor": "center"},
            "اللقب": {"text": "👤 اللقب", "width": 0, "minwidth": 0, "anchor": "center"},
            "الاسم": {"text": "📝 الاسم", "width": 200, "anchor": "center", "stretch": True},
            "الهاتف": {"text": "📞 الهاتف", "width": 0, "minwidth": 0, "anchor": "center"},
            "البريد الإلكتروني": {"text": "📧 البريد الإلكتروني", "width": 0, "minwidth": 0, "anchor": "center"},
            "المدينة": {"text": "🏙️ المدينة", "width": 0, "minwidth": 0, "anchor": "center"},
            "المنطقة": {"text": "📍 المنطقة", "width": 0, "minwidth": 0, "anchor": "center"},
            "الشارع": {"text": "🛣️ الشارع", "width": 0, "minwidth": 0, "anchor": "center"},
            "المبنى": {"text": "🏢 المبنى", "width": 0, "minwidth": 0, "anchor": "center"},
            "الرقم الضريبي": {"text": "🔢 الرقم الضريبي", "width": 0, "minwidth": 0, "anchor": "center"},
            "تاريخ الإدخال": {"text": "📅 تاريخ الإدخال", "width": 150, "anchor": "center"}
        }
        
        for col in columns:
            config = column_configs[col]
            self.clients_tree.heading(col, text=config["text"], anchor=tk.CENTER)
            self.clients_tree.column(col, anchor=config["anchor"], width=config["width"], minwidth=50)

        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.clients_tree.yview)
        self.clients_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.clients_tree.xview)
        self.clients_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.clients_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # تكوين الشبكة
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # تحميل بيانات العملاء
        self.load_clients()
        self.update_status("✅ تم تحميل بيانات العملاء بنجاح")

    def load_clients(self):
        for i in self.clients_tree.get_children():
            self.clients_tree.delete(i)
        try:
            self.cursor.execute("SELECT id, title, name, phone, email, city, district, street, building, tax_number, created_at FROM clients ORDER BY name ASC")
            clients = self.cursor.fetchall()
            for client in clients:
                # Format the date to show only the date part
                client_data = list(client)
                client_data[-1] = client_data[-1].split(' ')[0] # Get only the date part (YYYY-MM-DD)
                self.clients_tree.insert("", "end", values=client_data)
        except sqlite3.Error as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل تحميل العملاء:\n{e}")

        # تحديث عدد العملاء في شريط الحالة
        self.update_status_counts()

    def filter_clients(self, event=None):
        search_term = self.client_search_entry.get().strip().lower()
        for i in self.clients_tree.get_children():
            self.clients_tree.delete(i)
        try:
            query = "SELECT id, title, name, phone, email, city, district, street, building, tax_number, created_at FROM clients"
            if search_term:
                query += f" WHERE LOWER(name) LIKE '%{search_term}%' OR LOWER(created_at) LIKE '%{search_term}%'"
            query += " ORDER BY name ASC"
            self.cursor.execute(query)
            clients = self.cursor.fetchall()
            for client in clients:
                client_data = list(client)
                client_data[-1] = client_data[-1].split(' ')[0] # Get only the date part (YYYY-MM-DD)
                self.clients_tree.insert("", "end", values=client_data)
        except sqlite3.Error as e:
            messagebox.showerror("خطأ في البحث", f"فشل البحث عن العملاء:\n{e}")

    def add_client(self):
        """إضافة عميل جديد"""
        self.add_client_popup()
    
    def edit_client(self):
        """تعديل العميل المحدد"""
        selected = self.clients_tree.selection()
        if not selected:
            messagebox.showwarning("⚠️ تحذير", "يرجى اختيار عميل للتعديل أولاً")
            return
        # يمكن إضافة نافذة التعديل هنا
        messagebox.showinfo("قريباً", "ميزة التعديل ستكون متاحة قريباً")
    
    def delete_client(self):
        """حذف العميل المحدد"""
        selected = self.clients_tree.selection()
        if not selected:
            messagebox.showwarning("⚠️ تحذير", "يرجى اختيار عميل للحذف أولاً")
            return
        
        if messagebox.askyesno("🗑️ تأكيد الحذف", """هل أنت متأكد من حذف هذا العميل؟

⚠️ هذا الإجراء لا يمكن التراجع عنه!"""):
            try:
                item = self.clients_tree.item(selected[0])
                client_id = item['values'][0]
                self.cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
                self.db_conn.commit()
                self.load_clients()
                messagebox.showinfo("✅ نجح", "تم حذف العميل بنجاح!")
            except sqlite3.Error as e:
                messagebox.showerror("خطأ", f"""فشل حذف العميل:
{e}""")

    def setup_client_form_panel(self):
        # عنوان النموذج
        title_label = ttk.Label(self.client_form_panel, text="👤 بيانات العميل", style="Title.TLabel")
        title_label.pack(pady=(0, 10))

        # إطار النموذج
        form_frame = ttk.Frame(self.client_form_panel, style="Card.TFrame", padding="15")
        form_frame.pack(fill="both", expand=True)

        self.client_form_entries = {} # Use a class attribute for entries

        # تكوين الحقول مع أيقونات وتحسينات
        fields_config = [
            {"key": "title", "label": "👤 اللقب:", "type": "combo", "options": ["", "السيد", "السيدة", "مؤسسة", "شركة"]},
            {"key": "name", "label": "📝 الاسم (مطلوب):", "type": "entry", "required": True},
            {"key": "tax_number", "label": "🔢 الرقم الضريبي:", "type": "entry"},
            {"key": "phone", "label": "📞 الهاتف:", "type": "entry"},
            {"key": "email", "label": "📧 البريد الإلكتروني:", "type": "entry"},
            {"key": "city", "label": "🏙️ المدينة:", "type": "entry"},
            {"key": "district", "label": "📍 المنطقة:", "type": "entry"},
            {"key": "street", "label": "🛣️ الشارع:", "type": "entry"},
            {"key": "building", "label": "🏢 المبنى:", "type": "entry"}
        ]

        for i, field in enumerate(fields_config):
            label_text = field["label"]
            if field.get("required"):
                label_text += " *"

            label = ttk.Label(form_frame, text=label_text, style="Heading.TLabel")
            label.grid(row=i, column=0, sticky="w", pady=(5, 2), padx=(10, 0))

            if field["type"] == "combo":
                widget = ttk.Combobox(form_frame, values=field["options"],
                                    state="readonly", style="Modern.TCombobox")
                widget.set(field["options"][0])
            else:
                widget = ttk.Entry(form_frame, style="Modern.TEntry")

            widget.grid(row=i, column=1, sticky="ew", pady=(5, 2), padx=(0, 10))
            self.client_form_entries[field["key"]] = widget
            form_frame.grid_rowconfigure(i, weight=1)

        form_frame.columnconfigure(1, weight=1)

        # إطار الأزرار
        buttons_frame = ttk.Frame(self.client_form_panel, padding="15")
        buttons_frame.pack(fill="x", pady=(10, 0))

        save_btn = ttk.Button(buttons_frame, text="💾 حفظ العميل",
                            style="Success.TButton", command=self.save_client_form)
        save_btn.pack(side="right", padx=(10, 0))

        cancel_btn = ttk.Button(buttons_frame, text="❌ إلغاء",
                              style="Danger.TButton", command=self.hide_client_form)
        cancel_btn.pack(side="right")

        note_label = ttk.Label(buttons_frame,
                             text="* الحقول المطلوبة",
                             font=self.fonts['small'],
                             foreground="black")
        note_label.pack(side="left")

        # ربط اختصارات لوحة المفاتيح (على مستوى النافذة الرئيسية)
        self.root.bind("<Control-s>", lambda event: self.save_client_form() if self.client_form_panel.winfo_ismapped() else None)
        self.root.bind("<Escape>", lambda event: self.hide_client_form() if self.client_form_panel.winfo_ismapped() else None)

    def show_client_form(self):
        self.client_form_panel.grid() # Show the panel
        self.client_form_panel.tkraise() # Bring to front

    def hide_client_form(self):
        self.client_form_panel.grid_remove() # Hide the panel
        self.clear_client_form() # Clear fields when hidden

    def clear_client_form(self):
        for key, entry in self.client_form_entries.items():
            if key == "title":
                entry.set("")
            else:
                entry.delete(0, tk.END)
        self.current_editing_client_id = None # Clear current editing client ID

    def setup_item_form_panel(self):
        # عنوان النموذج (قابل للتحديث)
        self.item_form_title_label = ttk.Label(self.item_form_panel, text="🛒 بيانات الصنف", style="Title.TLabel")
        self.item_form_title_label.pack(pady=(0, 10))

        # إطار النموذج
        form_frame = ttk.Frame(self.item_form_panel, style="Card.TFrame", padding="15")
        form_frame.pack(fill="both", expand=True)

        self.item_form_entries = {} # Use a class attribute for entries

        # تكوين الحقول
        fields_config = [
            {"key": "code", "label": "رقم الصنف:", "type": "entry", "required": True},
            {"key": "desc", "label": "وصف الصنف:", "type": "entry", "required": True},
            {"key": "qty", "label": "الكمية:", "type": "entry", "required": True},
            {"key": "price", "label": "السعر:", "type": "entry", "required": True}
        ]

        for i, field in enumerate(fields_config):
            label_text = field["label"]
            if field.get("required"):
                label_text += " *"

            label = ttk.Label(form_frame, text=label_text, style="Heading.TLabel")
            label.grid(row=i, column=0, sticky="w", pady=(5, 2), padx=(10, 0))

            widget = ttk.Entry(form_frame, style="Modern.TEntry")
            widget.grid(row=i, column=1, sticky="ew", pady=(5, 2), padx=(0, 10))
            self.item_form_entries[field["key"]] = widget
            form_frame.grid_rowconfigure(i, weight=1)

        form_frame.columnconfigure(1, weight=1)

        # إطار الأزرار
        buttons_frame = ttk.Frame(self.item_form_panel, padding="15")
        buttons_frame.pack(fill="x", pady=(10, 0))

        save_btn = ttk.Button(buttons_frame, text="💾 حفظ الصنف",
                            style="Success.TButton", command=self.save_item_form)
        save_btn.pack(side="right", padx=(10, 0))

        cancel_btn = ttk.Button(buttons_frame, text="❌ إلغاء",
                              style="Danger.TButton", command=self.hide_item_form)
        cancel_btn.pack(side="right")

        note_label = ttk.Label(buttons_frame,
                             text="* الحقول المطلوبة",
                             font=self.fonts['small'],
                             foreground="black")
        note_label.pack(side="left")

        # ربط اختصارات لوحة المفاتيح (على مستوى النافذة الرئيسية)
        self.root.bind("<Control-i>", lambda event: self.save_item_form() if self.item_form_panel.winfo_ismapped() else None)
        self.root.bind("<Escape>", lambda event: self.hide_item_form() if self.item_form_panel.winfo_ismapped() else None)
        
        # إعداد أحداث الجدول
        self.setup_items_tree_events()

    def update_item_form_title(self, title):
        """تحديث عنوان اللوحة الجانبية للأصناف"""
        if hasattr(self, 'item_form_title_label'):
            self.item_form_title_label.config(text=title)
    
    def setup_items_tree_events(self):
        """إعداد أحداث جدول الأصناف"""
        if hasattr(self, 'items_tree'):
            # ربط النقر المزدوج بالتعديل المباشر
            self.items_tree.bind("<Double-1>", lambda event: self.edit_item_side_panel())

    def show_item_form(self):
        """إظهار اللوحة الجانبية للأصناف"""
        self.item_form_panel.grid() # Show the panel
        self.item_form_panel.tkraise() # Bring to front
        
        # إخفاء الرسالة التوضيحية إذا كانت موجودة
        if hasattr(self, 'item_form_placeholder'):
            self.item_form_placeholder.grid_remove()

    def hide_item_form(self):
        """إخفاء اللوحة الجانبية للأصناف"""
        self.item_form_panel.grid_remove() # Hide the panel
        self.clear_item_form() # Clear fields when hidden
        
        # إظهار الرسالة التوضيحية إذا كانت موجودة
        if hasattr(self, 'item_form_placeholder'):
            self.item_form_placeholder.grid()

    def clear_item_form(self):
        for key, entry in self.item_form_entries.items():
            entry.delete(0, tk.END)
        self.current_editing_item_idx = None # Clear current editing item index

    def save_item_form(self):
        code = self.item_form_entries["code"].get().strip()
        desc = self.item_form_entries["desc"].get().strip()
        qty = self.item_form_entries["qty"].get().strip()
        price = self.item_form_entries["price"].get().strip()

        if not code or not desc or not qty or not price:
            messagebox.showwarning("تنبيه", "جميع الحقول مطلوبة!")
            return

        item_data = {"code": code, "desc": desc, "qty": qty, "price": price}

        if self.current_editing_item_idx is not None:
            # Update existing item
            self.items_data[self.current_editing_item_idx] = item_data
            messagebox.showinfo("نجاح", "تم تعديل الصنف بنجاح!")
        else:
            # Add new item
            self.items_data.append(item_data)
            messagebox.showinfo("نجاح", "تم إضافة الصنف بنجاح!")
        
        self.refresh_items_treeview()
        self.hide_item_form()

    def save_client_form(self):
        title = self.client_form_entries["title"].get().strip()
        name = self.client_form_entries["name"].get().strip()
        tax_number = self.client_form_entries["tax_number"].get().strip()
        phone = self.client_form_entries["phone"].get().strip()
        email = self.client_form_entries["email"].get().strip()
        city = self.client_form_entries["city"].get().strip()
        district = self.client_form_entries["district"].get().strip()
        street = self.client_form_entries["street"].get().strip()
        building = self.client_form_entries["building"].get().strip()

        if not name:
            messagebox.showerror("خطأ", "الاسم لا يمكن أن يكون فارغًا.")
            return

        try:
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'current_editing_client_id') and self.current_editing_client_id:
                # Update existing client
                self.cursor.execute("""
                    UPDATE clients SET title=?, name=?, tax_number=?, phone=?, email=?, city=?, district=?, street=?, building=?
                    WHERE id=?
                """, (title, name, tax_number, phone, email, city, district, street, building, self.current_editing_client_id))
                messagebox.showinfo("✅ نجح!", "تم تعديل العميل بنجاح! 🎉")
            else:
                # Add new client
                self.cursor.execute("""
                    INSERT INTO clients (title, name, tax_number, phone, email, city, district, street, building, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (title, name, tax_number, phone, email, city, district, street, building, created_at))
                messagebox.showinfo("✅ نجح!", "تم إضافة العميل الجديد بنجاح! 🎉")

            self.db_conn.commit()
            self.load_clients()
            self.load_clients_combo()
            self.update_status("✅ تم تحديث بيانات العميل بنجاح")
            self.hide_client_form() # Hide form after saving
        except sqlite3.Error as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"""فشل حفظ العميل:
{e}""")

    def add_client(self, event=None):
        """إضافة عميل جديد"""
        self.clear_client_form()
        self.show_client_form()

    def edit_client(self, event=None):
        """تعديل العميل المحدد"""
        selected = self.clients_tree.selection()
        if not selected:
            messagebox.showwarning("⚠️ تحذير", "يرجى اختيار عميل للتعديل أولاً")
            return

        item = self.clients_tree.item(selected[0])
        client_data = item['values']

        self.current_editing_client_id = client_data[0] # Store ID for update

        # Populate form fields
        self.client_form_entries["title"].set(client_data[1])
        self.client_form_entries["name"].delete(0, tk.END)
        self.client_form_entries["name"].insert(0, client_data[2])
        self.client_form_entries["tax_number"].delete(0, tk.END)
        self.client_form_entries["tax_number"].insert(0, client_data[9])
        self.client_form_entries["phone"].delete(0, tk.END)
        self.client_form_entries["phone"].insert(0, client_data[3])
        self.client_form_entries["email"].delete(0, tk.END)
        self.client_form_entries["email"].insert(0, client_data[4])
        self.client_form_entries["city"].delete(0, tk.END)
        self.client_form_entries["city"].insert(0, client_data[5])
        self.client_form_entries["district"].delete(0, tk.END)
        self.client_form_entries["district"].insert(0, client_data[6])
        self.client_form_entries["street"].delete(0, tk.END)
        self.client_form_entries["street"].insert(0, client_data[7])
        self.client_form_entries["building"].delete(0, tk.END)
        self.client_form_entries["building"].insert(0, client_data[8])
        self.client_form_entries["tax_number"].delete(0, tk.END)
        self.client_form_entries["tax_number"].insert(0, client_data[9])

        self.show_client_form()

    def delete_selected_client(self):
        selected_item = self.clients_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد عميل لحذفه.")
            return

        client_id = self.clients_tree.item(selected_item, "values")[0]
        client_name = self.clients_tree.item(selected_item, "values")[2] # الاسم في الفهرس 2

        if messagebox.askyesno("تأكيد الحذف", f"""هل أنت متأكد أنك تريد حذف العميل '{client_name}' (ID: {client_id})?

ملاحظة: سيتم حذف جميع العقود المرتبطة بهذا العميل.""", icon='warning'):
            try:
                self.cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
                self.db_conn.commit()
                messagebox.showinfo("نجاح", "تم حذف العميل بنجاح.")
                self.load_clients()
                self.load_clients_combo() # تحديث قائمة العملاء في تبويب العقود
            except sqlite3.Error as e:
                messagebox.showerror("خطأ في قاعدة البيانات", f"""فشل حذف العميل:
{e}""")

    # --- تبويب القوالب ---
    def setup_templates_tab(self):
        # إنشاء صفحة قابلة للتمرير
        canvas, scrollable_frame = self.create_scrollable_page(self.templates_tab)

        # إطار العنوان الرئيسي مع محاذاة وسط
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))

        title_label = ttk.Label(title_frame, text="📄 إدارة قوالب العقود", style="Title.TLabel")
        title_label.pack(anchor="center")

        # إطار الأدوات مع محاذاة وسط
        tools_frame = ttk.Frame(scrollable_frame, style="Card.TFrame", padding="20")
        tools_frame.pack(fill="x", padx=40, pady=(0, 20), anchor="center")
        
        # إطار الأزرار مع محاذاة وسط
        buttons_container = ttk.Frame(tools_frame)
        buttons_container.pack(anchor="center")

        # أزرار الإجراءات
        upload_btn = ttk.Button(buttons_container, text="⬆️ رفع قالب جديد",
                              style="Success.TButton", command=self.upload_template)
        upload_btn.pack(side="left", padx=(0, 15))
        # إضافة أحداث التمرير
        upload_btn.bind("<Enter>", lambda e: self.show_element_info("زر رفع قالب", extra_info="رفع قالب Word جديد للنظام"))
        upload_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        delete_template_btn = ttk.Button(buttons_container, text="🗑️ حذف القالب",
                                       style="Danger.TButton", command=self.delete_template)
        delete_template_btn.pack(side="left", padx=(0, 15))
        # إضافة أحداث التمرير
        delete_template_btn.bind("<Enter>", lambda e: self.show_element_info("زر حذف القالب", extra_info="حذف القالب المحدد نهائياً"))
        delete_template_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        preview_btn = ttk.Button(buttons_container, text="👁️ معاينة القالب",
                               style="Primary.TButton", command=self.preview_template)
        preview_btn.pack(side="left", padx=(0, 15))
        # إضافة أحداث التمرير
        preview_btn.bind("<Enter>", lambda e: self.show_element_info("زر معاينة القالب", extra_info="عرض محتوى القالب المحدد"))
        preview_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        # زر دليل المتغيرات
        guide_btn = ttk.Button(buttons_container, text="📖 دليل المتغيرات",
                             style="Info.TButton", command=self.show_variables_guide)
        guide_btn.pack(side="left")
        # إضافة أحداث التمرير
        guide_btn.bind("<Enter>", lambda e: self.show_element_info("زر دليل المتغيرات", extra_info="عرض دليل المتغيرات المتاحة"))
        guide_btn.bind("<Leave>", lambda e: self.clear_hover_info())
        
        # إطار عرض القوالب كبطاقات مع محاذاة وسط
        templates_display_frame = ttk.Frame(scrollable_frame, style="Card.TFrame", padding="20")
        templates_display_frame.pack(fill="both", expand=True, padx=40, pady=(0, 40), anchor="center")

        templates_title = ttk.Label(templates_display_frame, text="📋 قائمة القوالب المتاحة", style="Heading.TLabel")
        templates_title.pack(anchor="center", pady=(0, 15))

        # Canvas for scrollable template cards
        self.templates_canvas = tk.Canvas(templates_display_frame, borderwidth=0, background=self.colors['bg_card'], highlightthickness=0)
        self.templates_canvas.pack(side="left", fill="both", expand=True)

        self.templates_scrollbar = ttk.Scrollbar(templates_display_frame, orient="vertical", command=self.templates_canvas.yview)
        self.templates_scrollbar.pack(side="right", fill="y")

        self.templates_canvas.configure(yscrollcommand=self.templates_scrollbar.set)
        self.templates_canvas.bind('<Configure>', lambda e: self.templates_canvas.configure(scrollregion=self.templates_canvas.bbox("all")))

        self.templates_card_container = ttk.Frame(self.templates_canvas)
        self.templates_canvas.create_window((0, 0), window=self.templates_card_container, anchor="nw")

        # Update inner frame size when canvas size changes
        self.templates_card_container.bind("<Configure>", lambda e: self.templates_canvas.configure(scrollregion = self.templates_canvas.bbox("all")))
        
        # Track selected template
        self.selected_template_id = None
        self.selected_template_name = None
        self.selected_template_card = None # To store the currently selected card widget

        # تحميل بيانات القوالب
        self.load_templates()
    
    def setup_status_bar(self):
        """إعداد شريط الحالة في أسفل التطبيق"""
        self.status_frame = ttk.Frame(self.root, style="Card.TFrame", padding="5")
        self.status_frame.pack(side="bottom", fill="x", padx=10, pady=(0, 10))

        # أيقونة الحالة
        self.status_icon = ttk.Label(self.status_frame, text="🟢", font=self.fonts['body'])
        self.status_icon.pack(side="right", padx=(0, 5))

        # نص الحالة
        self.status_label = ttk.Label(self.status_frame, text="جاهز للاستخدام",
                                    font=self.fonts['small'],
                                    foreground=self.colors['text_secondary'])
        self.status_label.pack(side="right", padx=(0, 10))

        # معلومات النسخة
        version_label = ttk.Label(self.status_frame, text="نظام إدارة العقود الذكي v2.0",
                                font=self.fonts['small'],
                                foreground=self.colors['text_secondary'])
        version_label.pack(side="left")

        # عدد العملاء
        self.clients_count_label = ttk.Label(self.status_frame, text="👥 العملاء: 0",
                                           font=self.fonts['small'],
                                           foreground=self.colors['text_secondary'])
        self.clients_count_label.pack(side="left", padx=(20, 0))

        # عدد القوالب
        self.templates_count_label = ttk.Label(self.status_frame, text="📄 القوالب: 0",
                                             font=self.fonts['small'],
                                             foreground=self.colors['text_secondary'])
        self.templates_count_label.pack(side="left", padx=(10, 0))

        # منطقة المعلومات الديناميكية (عند التمرير على العناصر)
        self.hover_info_label = ttk.Label(self.status_frame, text="",
                                        font=self.fonts['small'],
                                        foreground=self.colors['accent'])
        self.hover_info_label.pack(side="left", padx=(20, 0))

        # الوقت الحالي
        self.time_label = ttk.Label(self.status_frame, text="",
                                  font=self.fonts['small'],
                                  foreground=self.colors['text_secondary'])
        self.time_label.pack(side="left", padx=(20, 0))

        # تحديث الوقت كل ثانية
        self.update_time()

        # تحديث الإحصائيات
        self.update_status_counts()
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M:%S")
        self.time_label.config(text=f"📅 {current_time}")
        self.root.after(1000, self.update_time)  # تحديث كل ثانية
    
    def update_status(self, message, icon="🟢"):
        """تحديث رسالة الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
            self.status_icon.config(text=icon)

    def update_status_counts(self):
        """تحديث عدادات العناصر في شريط الحالة"""
        try:
            # تحديث عدد العملاء
            if hasattr(self, 'clients_count_label'):
                self.cursor.execute("SELECT COUNT(*) FROM clients")
                clients_count = self.cursor.fetchone()[0]
                self.clients_count_label.config(text=f"👥 العملاء: {clients_count}")

            # تحديث عدد القوالب
            if hasattr(self, 'templates_count_label'):
                self.cursor.execute("SELECT COUNT(*) FROM templates")
                templates_count = self.cursor.fetchone()[0]
                self.templates_count_label.config(text=f"📄 القوالب: {templates_count}")

        except sqlite3.Error as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def update_hover_info(self, info_text):
        """تحديث المعلومات الديناميكية عند التمرير"""
        if hasattr(self, 'hover_info_label'):
            self.hover_info_label.config(text=info_text)

    def clear_hover_info(self):
        """مسح المعلومات الديناميكية"""
        if hasattr(self, 'hover_info_label'):
            self.hover_info_label.config(text="")

    def show_element_info(self, element_name, width=None, height=None, size=None, extra_info=""):
        """عرض معلومات العنصر عند التمرير عليه"""
        info_parts = [f"📍 {element_name}"]

        if width and height:
            info_parts.append(f"📐 {width}×{height}")
        elif size:
            info_parts.append(f"📏 {size}")

        if extra_info:
            info_parts.append(extra_info)

        info_text = " | ".join(info_parts)
        self.update_hover_info(info_text)

    def show_template_card_info(self, template_name, variables_count, file_size=None):
        """عرض معلومات بطاقة القالب عند التمرير عليها"""
        info_parts = [f"📄 {template_name}"]
        info_parts.append(f"🔢 {variables_count} متغير")

        if file_size:
            if file_size > 1024*1024:  # أكبر من 1 ميجا
                size_str = f"{file_size/(1024*1024):.1f} ميجا"
            elif file_size > 1024:  # أكبر من 1 كيلو
                size_str = f"{file_size/1024:.1f} كيلو"
            else:
                size_str = f"{file_size} بايت"
            info_parts.append(f"📏 {size_str}")

        info_text = " | ".join(info_parts)
        self.update_hover_info(info_text)
    
    def load_templates(self):
        # Clear existing cards
        for widget in self.templates_card_container.winfo_children():
            widget.destroy()
        
        self.template_files = {} # مسح القاموس قبل التحميل
        self.template_buttons = {} # Store references to template buttons for selection highlighting

        try:
            self.cursor.execute("SELECT id, name, file_path, variables FROM templates ORDER BY name ASC")
            templates = self.cursor.fetchall()
            
            col = 0
            row = 0
            for tpl_id, name, file_path, variables_json in templates:
                has_items_table = False
                try:
                    # --- فحص وجود جدول أصناف ---
                    from docx import Document
                    docx_doc = Document(file_path)
                    for r in docx_doc.tables: # Renamed 'row' to 'r' to avoid conflict with loop variable
                        for cell in r.rows:
                            for c in cell.cells:
                                if '{% for item in items %}' in c.text:
                                    has_items_table = True
                                    break
                        if has_items_table:
                            break
                except Exception as e:
                    print(f"فشل فحص جدول الأصناف في القالب: {e}")

                self.template_has_items_table[name] = has_items_table
                display_name = name + (" 🛒" if has_items_table else "")

                # Create a button for each template
                template_button = ttk.Button(self.templates_card_container,
                                             text=display_name,
                                             style="TemplateCard.TButton",
                                             command=lambda t_id=tpl_id, t_name=name: self.on_template_card_selected(t_id, t_name))
                template_button.grid(row=row, column=col, padx=5, pady=5, sticky="ew")

                # إضافة أحداث التمرير لبطاقة القالب
                try:
                    # حساب عدد المتغيرات
                    variables = json.loads(variables_json) if variables_json else []
                    variables_count = len(variables)

                    # حساب حجم الملف
                    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else None

                    # ربط أحداث التمرير
                    template_button.bind("<Enter>",
                        lambda e, t_name=name, v_count=variables_count, f_size=file_size:
                        self.show_template_card_info(t_name, v_count, f_size))
                    template_button.bind("<Leave>", lambda e: self.clear_hover_info())
                except Exception as e:
                    print(f"خطأ في إضافة أحداث التمرير للقالب {name}: {e}")

                # Store button reference
                self.template_buttons[tpl_id] = template_button

                self.template_files[name] = file_path # تخزين مسار الملف لسهولة الوصول إليه

                col += 1
                if col > 3: # 4 columns per row
                    col = 0
                    row += 1
            
            # Configure column weights for even distribution
            for i in range(4):
                self.templates_card_container.grid_columnconfigure(i, weight=1)

        except sqlite3.Error as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل تحميل القوالب:\n{e}")

    def on_template_card_selected(self, tpl_id, tpl_name):
        # Deselect previous card if any
        if self.selected_template_card:
            self.selected_template_card.state(['!selected'])
        
        # Select new card
        self.selected_template_id = tpl_id
        self.selected_template_name = tpl_name
        self.selected_template_card = self.template_buttons[tpl_id] # Get the button reference
        if self.selected_template_card:
            self.selected_template_card.state(['selected'])
        
        self.update_status(f"✅ تم اختيار القالب: {tpl_name}")

    def delete_template(self):
        """حذف القالب المحدد"""
        if not self.selected_template_id:
            messagebox.showwarning("تحذير", "يرجى اختيار قالب للحذف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف القالب {self.selected_template_name}؟"):
            try:
                # Get file path before deleting from DB
                self.cursor.execute("SELECT file_path FROM templates WHERE id = ?", (self.selected_template_id,))
                file_path = self.cursor.fetchone()[0]

                self.cursor.execute("DELETE FROM templates WHERE id = ?", (self.selected_template_id,))
                self.db_conn.commit()
                
                # Delete the actual file from the templates folder
                if os.path.exists(file_path):
                    os.remove(file_path)
                
                messagebox.showinfo("✅ تم الحذف!", "تم حذف القالب بنجاح! 🗑️")
                self.load_templates()
                self.load_templates_combo() # Update template list in contracts tab
                self.update_status_counts() # تحديث عدد القوالب في شريط الحالة
                self.selected_template_id = None # Clear selection
                self.selected_template_name = None
                self.selected_template_card = None
            except sqlite3.Error as e:
                messagebox.showerror("خطأ", f"""فشل حذف القالب:
{e}""")
            except Exception as e:
                messagebox.showerror("خطأ في الملف", f"""فشل حذف ملف القالب:
{e}""")

    def preview_template(self):
        """معاينة القالب المحدد"""
        if not self.selected_template_id:
            messagebox.showwarning("تحذير", "يرجى اختيار قالب للمعاينة")
            return
        messagebox.showinfo("🔜 قريباً", f"ميزة معاينة القالب {self.selected_template_name} ستكون متاحة قريباً! ⏳")

    def show_variables_guide(self):
        """عرض دليل كتابة المتغيرات"""
        guide_text = """📖 دليل كتابة المتغيرات في قوالب Word

🔹 القواعد الأساسية:
• استخدم {{ }} حول اسم المتغير
• ابدأ اسم المتغير بحرف أو شرطة سفلية
• استخدم الحروف والأرقام والشرطة السفلية فقط

✅ أمثلة صحيحة:
• {{اسم_العميل}}
• {{قيمة_العقد}}
• {{الضريبة_15}}
• {{client_name}}
• {{_المبلغ}}

❌ أمثلة خاطئة:
• {{15_الضريبة}} (يبدأ برقم)
• {{اسم العميل}} (يحتوي على مسافة)
• {{قيمة@العقد}} (يحتوي على رمز خاص)
• {{print}} (كلمة محجوزة)

🔹 متغيرات خاصة:
• {{items}} - لجدول الأصناف
• يجب استخدام حلقة للأصناف:
  {% for item in items %}
  {{item.code}} - {{item.desc}}
  {% endfor %}

🔹 نصائح إضافية:
• استخدم أسماء واضحة ومفهومة
• تجنب الأسماء الطويلة جداً
• استخدم الشرطة السفلية بدلاً من المسافات
• تأكد من إغلاق كل {{ بـ }}"""
        
        messagebox.showinfo("📖 دليل المتغيرات", guide_text)

    def validate_docx_file(self, file_path):
        """التحقق من صحة ملف Word"""
        try:
            # فحص امتداد الملف
            if not file_path.lower().endswith('.docx'):
                return False, "الملف يجب أن يكون بصيغة .docx"
            
            # فحص حجم الملف (لا يجب أن يكون فارغاً أو كبيراً جداً)
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "الملف فارغ"
            elif file_size > 50 * 1024 * 1024:  # 50 MB
                return False, "حجم الملف كبير جداً (أكثر من 50 ميجابايت)"
            
            # محاولة فتح الملف كـ docx
            from docx import Document
            doc = Document(file_path)
            
            # فحص أساسي للمحتوى
            if len(doc.paragraphs) == 0 and len(doc.tables) == 0:
                return False, "الملف لا يحتوي على محتوى"
            
            return True, "الملف صحيح"
            
        except Exception as e:
            return False, f"خطأ في فحص الملف: {str(e)}"

    def upload_template(self):
        file_path = filedialog.askopenfilename(
            title="اختر قالب Word (docx)",
            filetypes=[("Word Documents", "*.docx")]
        )
        if not file_path:
            return

        # التحقق من صحة الملف قبل المعالجة
        is_valid, validation_message = self.validate_docx_file(file_path)
        if not is_valid:
            messagebox.showerror("ملف غير صحيح", f"لا يمكن رفع الملف:\n\n{validation_message}")
            return

        template_name = os.path.basename(file_path)
        destination_path = os.path.join(TEMPLATES_DIR, template_name)

        if os.path.exists(destination_path):
            # التحقق مما إذا كان الملف المصدر هو نفسه الملف الوجهة
            if os.path.samefile(file_path, destination_path):
                response = messagebox.askyesno(
                    "القالب موجود بالفعل",
                    f"القالب '{template_name}' موجود بالفعل في مجلد القوالب. هل تريد تحديث معلوماته في قاعدة البيانات؟"
                )
                if not response:
                    return
            else:
                response = messagebox.askyesno(
                    "القالب موجود بالفعل",
                    f"القالب '{template_name}' موجود بالفعل. هل تريد استبداله؟"
                )
                if not response:
                    return

        try:
            # لا تقم بالنسخ إذا كان الملف المصدر هو نفسه الملف الوجهة
            if not (os.path.exists(destination_path) and os.path.samefile(file_path, destination_path)):
                shutil.copy(file_path, destination_path)
            
            # التحقق من صحة الملف أولاً
            try:
                # محاولة فتح الملف كـ docx عادي للتحقق من صحته
                from docx import Document
                test_doc = Document(destination_path)
                # إذا وصلنا هنا، فالملف صحيح
            except Exception as validation_error:
                raise Exception(f"الملف المرفوع ليس ملف Word صحيح أو تالف: {validation_error}")
            
            # محاولة قراءة المتغيرات من القالب الجديد
            try:
                doc = DocxTemplate(destination_path)
                all_vars = doc.undeclared_template_variables
                filtered_vars = [v for v in all_vars if '.' not in v and not v.startswith('__')]
                variables_json = json.dumps(sorted(filtered_vars))
            except Exception as template_error:
                # إذا فشل DocxTemplate، نحاول حفظ القالب بدون متغيرات
                print(f"تحذير: فشل في قراءة متغيرات القالب: {template_error}")
                filtered_vars = []
                variables_json = json.dumps([])

            # --- فحص وجود جدول أصناف ---
            from docx import Document
            has_items_table = False
            try:
                docx_doc = Document(destination_path)
                for table in docx_doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            if '{% for item in items %}' in cell.text:
                                has_items_table = True
                                break
                    if has_items_table:
                        break
            except Exception as e:
                print(f"فشل فحص جدول الأصناف في القالب: {e}")
            self.template_has_items_table[template_name] = has_items_table
            if has_items_table:
                messagebox.showinfo("جدول أصناف", f"القالب '{template_name}' يدعم جدول أصناف (🛒). ستظهر لك واجهة الأصناف عند استخدامه.")

            # حفظ في قاعدة البيانات
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.cursor.execute("INSERT OR REPLACE INTO templates (name, file_path, variables, created_at) VALUES (?, ?, ?, ?)",
                                (template_name, destination_path, variables_json, created_at))
            self.db_conn.commit()
            messagebox.showinfo("نجاح", f"تم رفع القالب '{template_name}' بنجاح.")
            self.load_templates() # إعادة تحميل قائمة القوالب
            self.load_templates_combo() # إعادة تحميل قائمة القوالب في تبويب العقود
            self.update_status_counts() # تحديث عدد القوالب في شريط الحالة
        except Exception as e:
            error_msg = str(e)
            
            # تحسين رسائل الخطأ الشائعة
            if "unexpected char" in error_msg:
                error_msg = """الملف يحتوي على محارف أو تنسيق غير متوافق.

الحلول المقترحة:
• تأكد من أن الملف هو ملف Word (.docx) صحيح
• جرب حفظ الملف مرة أخرى من Microsoft Word
• تأكد من عدم وجود محارف خاصة في المتغيرات (استخدم {{ }} فقط)
• تجنب استخدام الرموز الخاصة مثل: @ # $ % ^ & *
• جرب إنشاء نسخة جديدة من القالب بدون تنسيق معقد"""
            elif "not a valid docx file" in error_msg.lower():
                error_msg = """الملف المرفوع ليس ملف Word صحيح.

تأكد من:
• الملف بصيغة .docx وليس .doc
• الملف غير تالف أو مضغوط
• الملف تم إنشاؤه بـ Microsoft Word أو برنامج متوافق
• لم يتم تعديل الملف بمحرر نصوص عادي"""
            elif "permission" in error_msg.lower():
                error_msg = """مشكلة في صلاحيات الوصول للملف.

جرب:
• إغلاق الملف إذا كان مفتوحاً في Word
• تشغيل البرنامج كمدير (Run as Administrator)
• نسخ الملف إلى مجلد آخر ثم رفعه"""
            
            messagebox.showerror("خطأ في رفع القالب", f"""فشل رفع القالب '{template_name}':

{error_msg}""")
            
            print(f"خطأ في رفع القالب {template_name}: {e}") # طباعة الخطأ للمساعدة في التصحيح
            
            # إذا فشل الرفع، حاول حذف الملف المنسوخ إذا كان موجودًا
            if os.path.exists(destination_path):
                try:
                    os.remove(destination_path)
                except:
                    pass
            
            # تسجيل الخطأ في الأرشيف
            error_title = f"خطأ في رفع القالب: {template_name}"
            error_content = f"**الخطأ:**\n```\n{e}\n```"
            self.append_to_documentation_archive(error_title, error_content)


    def delete_selected_template(self):
        selected_item = self.templates_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "الرجاء تحديد قالب لحذفه.")
            return

        tpl_id, tpl_name, _ = self.templates_tree.item(selected_item, "values")
        
        # التحقق مما إذا كانت هناك عقود مرتبطة بهذا القالب (بسبب ON DELETE RESTRICT)
        try:
            self.cursor.execute("SELECT COUNT(*) FROM contracts WHERE template_id = ?", (tpl_id,))
            contract_count = self.cursor.fetchone()[0]
            if contract_count > 0:
                messagebox.showerror(
                    "خطأ في الحذف",
                    f"لا يمكن حذف القالب '{tpl_name}' لأن هناك {contract_count} عقدًا مرتبطًا به.\n"
                    "يرجى حذف العقود المرتبطة أولاً."
                )
                return
        except sqlite3.Error as e:
            messagebox.showerror("خطأ في التحقق", f"""فشل التحقق من العقود المرتبطة:
{e}""")
            return


        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد أنك تريد حذف القالب '{tpl_name}' (ID: {tpl_id})؟", icon='warning'):
            try:
                # الحصول على مسار الملف قبل الحذف من DB
                self.cursor.execute("SELECT file_path FROM templates WHERE id = ?", (tpl_id,))
                file_path = self.cursor.fetchone()[0]

                self.cursor.execute("DELETE FROM templates WHERE id = ?", (tpl_id,))
                self.db_conn.commit()
                
                # حذف الملف الفعلي من مجلد القوالب
                if os.path.exists(file_path):
                    os.remove(file_path)
                
                messagebox.showinfo("نجاح", "تم حذف القالب بنجاح.")
                self.load_templates()
                self.load_templates_combo() # تحديث قائمة القوالب في تبويب العقود
            except sqlite3.Error as e:
                messagebox.showerror("خطأ في قاعدة البيانات", f"""فشل حذف القالب:
{e}""")
            except Exception as e:
                messagebox.showerror("خطأ في الملف", f"""فشل حذف ملف القالب:
{e}""")


    

    # --- تبويب توليد العقود ---
    def setup_contracts_tab(self):
        # إنشاء صفحة قابلة للتمرير
        canvas, scrollable_frame = self.create_scrollable_page(self.contracts_tab)

        # إطار العنوان الرئيسي
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        title_label = ttk.Label(title_frame, text="✨ مولد العقود الذكي", style="Title.TLabel")
        title_label.pack(side="right")
        
        # إطار رئيسي مع تحسينات
        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # إطار اختيار العميل مع تحسينات
        client_frame = ttk.LabelFrame(main_frame, text="👤 اختيار العميل", padding="15", style="Card.TFrame")
        client_frame.grid(row=0, column=0, padx=10, pady=(0, 15), sticky="ew")

        client_label = ttk.Label(client_frame, text="👤 اختر العميل:", style="Heading.TLabel")
        client_label.pack(anchor="e", pady=(0, 8))
        
        self.client_combo = ttk.Combobox(client_frame, state="readonly", style="Modern.TCombobox")
        self.client_combo.pack(fill="x", pady=(0, 5))
        self.client_combo.bind("<<ComboboxSelected>>", self.on_client_selected)
        self.load_clients_combo()

        # إطار اختيار القالب مع تحسينات
        template_frame = ttk.LabelFrame(main_frame, text="📄 اختيار القالب", padding="15", style="Card.TFrame")
        template_frame.grid(row=0, column=1, padx=10, pady=(0, 15), sticky="ew")

        template_label = ttk.Label(template_frame, text="📄 اختر قالب العقد:", style="Heading.TLabel")
        template_label.pack(anchor="e", pady=(0, 8))
        
        self.template_combo = ttk.Combobox(template_frame, state="readonly", style="Modern.TCombobox")
        self.template_combo.pack(fill="x", pady=(0, 5))
        self.template_combo.bind("<<ComboboxSelected>>", self.on_template_selected)

        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        main_frame.grid_rowconfigure(0, weight=0) # Ensure this row doesn't expand vertically unnecessarily

        # إطار حقول المتغيرات مع تحسينات
        self.variables_frame = ttk.LabelFrame(main_frame, text="📝 تعبئة بيانات العقد", padding="15", style="Card.TFrame")
        self.variables_frame.grid(row=2, column=0, columnspan=2, padx=10, pady=(0, 15), sticky="nsew")

        # منطقة عرض حقول المتغيرات مع تحسينات بصرية
        canvas_frame = ttk.Frame(self.variables_frame)
        canvas_frame.pack(fill="both", expand=True)
        
        self.variables_canvas = tk.Canvas(canvas_frame, borderwidth=0, background=self.colors['bg_card'], highlightthickness=0)
        self.variables_canvas.pack(side="left", fill="both", expand=True)

        self.variables_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.variables_canvas.yview)
        self.variables_scrollbar.pack(side="right", fill="y")

        self.variables_canvas.configure(yscrollcommand=self.variables_scrollbar.set)
        self.variables_canvas.bind('<Configure>', lambda e: self.variables_canvas.configure(scrollregion=self.variables_canvas.bbox("all")))

        self.inner_variables_frame = ttk.Frame(self.variables_canvas)
        self.variables_canvas.create_window((0, 0), window=self.inner_variables_frame, anchor="nw")

        # تحديث حجم الإطار الداخلي عند تغيير حجم النافذة
        self.inner_variables_frame.bind("<Configure>", lambda e: self.variables_canvas.configure(scrollregion = self.variables_canvas.bbox("all")))
        
        # تحديث عرض Canvas عند تغيير حجم النافذة الرئيسية
        self.variables_canvas.bind('<Configure>', self.on_canvas_configure)
        main_frame.grid_columnconfigure(1, weight=1) # لجعل عمود المتغيرات يتمدد

        # --- جدول الأصناف ---
        self.items_frame = ttk.LabelFrame(main_frame, text="🛒 جدول الأصناف", padding="15", style="Card.TFrame")
        self.items_frame.grid(row=4, column=0, columnspan=2, padx=10, pady=(0, 15), sticky="nsew")
        self.items_frame.grid_remove() # إخفاء الإطار افتراضياً
        
        # Configure items_frame to use grid for its internal layout
        self.items_frame.grid_columnconfigure(0, weight=1) # For the treeview
        self.items_frame.grid_columnconfigure(1, weight=0) # For the separator
        self.items_frame.grid_columnconfigure(2, weight=1) # For the item form
        self.items_frame.grid_rowconfigure(0, weight=1)

        # Treeview for items
        self.items_tree = ttk.Treeview(self.items_frame, columns=("code", "desc", "qty", "price"), show="headings", height=6)
        self.items_tree.heading("code", text="رقم الصنف")
        self.items_tree.heading("desc", text="وصف الصنف")
        self.items_tree.heading("qty", text="الكمية")
        self.items_tree.heading("price", text="السعر")
        self.items_tree.column("code", width=100, anchor="center")
        self.items_tree.column("desc", width=200, anchor="center", stretch=True)
        self.items_tree.column("qty", width=80, anchor="center")
        self.items_tree.column("price", width=100, anchor="center")
        self.items_tree.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        # Scrollbar for items_tree
        items_scrollbar = ttk.Scrollbar(self.items_frame, orient="vertical", command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        items_scrollbar.grid(row=0, column=1, sticky="ns")

        # Separator between treeview and form
        separator = ttk.Separator(self.items_frame, orient="vertical")
        separator.grid(row=0, column=1, sticky="ns", padx=5)

        # Item form panel
        self.item_form_panel = ttk.Frame(self.items_frame, style="Card.TFrame", padding="15")
        self.item_form_panel.grid(row=0, column=2, sticky="nsew", padx=(5, 0))
        self.setup_item_form_panel() # Call to create form fields
        self.hide_item_form() # Initially hide the form
        
        # قائمة الأصناف في الذاكرة
        self.items_data = []
        
        # أزرار إدارة الأصناف
        self.items_btns_frame = ttk.Frame(main_frame)
        self.items_btns_frame.grid(row=5, column=0, columnspan=2, sticky="ew", padx=10, pady=(0, 10))
        self.items_btns_frame.grid_remove() # إخفاء الإطار افتراضياً
        
        add_item_btn = ttk.Button(self.items_btns_frame, text="➕ إضافة صنف", style="Success.TButton", command=self.add_item_popup)
        add_item_btn.pack(side="right", padx=(0, 10))
        edit_item_btn = ttk.Button(self.items_btns_frame, text="✏️ تعديل صنف", style="Primary.TButton", command=self.edit_item_popup)
        edit_item_btn.pack(side="right", padx=(0, 10))
        del_item_btn = ttk.Button(self.items_btns_frame, text="🗑️ حذف صنف", style="Danger.TButton", command=self.delete_selected_item)
        del_item_btn.pack(side="right")

        # إطار أزرار الإجراءات مع تحسينات
        action_frame = ttk.Frame(main_frame, style="Card.TFrame", padding="20")
        action_frame.grid(row=6, column=0, columnspan=2, padx=5, pady=(0, 10), sticky="ew")
        
        # عنوان قسم الإجراءات
        action_title = ttk.Label(action_frame, text="🚀 إجراءات العقد", style="Heading.TLabel")
        action_title.pack(pady=(0, 15))
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(action_frame)
        buttons_frame.pack(fill="x")
        
        # زر توليد العقد
        gen_btn = ttk.Button(buttons_frame, text="⚙️ توليد العقد النهائي", 
                           style="Success.TButton", command=self.generate_contract)
        gen_btn.pack(side="right", padx=(10, 0), ipadx=20, ipady=5)

        # زر المعاينة المباشرة
        preview_btn = ttk.Button(buttons_frame, text="👁️ معاينة العقد", 
                               style="Primary.TButton", command=self.show_preview_window)
        preview_btn.pack(side="right", padx=(10, 0), ipadx=20, ipady=5)
        
        # زر حفظ كمسودة
        draft_btn = ttk.Button(buttons_frame, text="💾 حفظ كمسودة", 
                             style="Secondary.TButton", command=self.save_as_draft)
        draft_btn.pack(side="right", ipadx=20, ipady=5)

        # --- قسم الزكاة QR (يظهر فقط للفواتير) ---
        self.zatca_section_frame = ttk.LabelFrame(main_frame, text="🔳 مولد QR الزكاة", padding="15", style="Card.TFrame")
        self.zatca_section_frame.grid(row=6, column=0, columnspan=2, padx=10, pady=(0, 15), sticky="ew")
        self.zatca_section_frame.grid_remove() # إخفاء الإطار افتراضياً
        
        # إعداد قسم الزكاة QR داخل صفحة العقود
        self.setup_zatca_qr_section_in_contracts(self.zatca_section_frame)

        # سيتم تحديث رقم الصف لإطار الإجراءات لاحقاً

        # تحميل البيانات بعد إنشاء جميع العناصر
        self.load_templates_combo()



    def setup_zatca_qr_section_in_contracts(self, parent_frame):
        """إعداد قسم توليد QR الزكاة داخل صفحة العقود"""
        # إطار الحقول
        fields_frame = ttk.Frame(parent_frame)
        fields_frame.pack(fill="x", pady=(0, 15))
        
        # تكوين الشبكة
        fields_frame.grid_columnconfigure(1, weight=1)
        
        # حقول البيانات
        self.zatca_entries = {}
        
        zatca_fields = [
            {"key": "seller_name", "label": "اسم البائع:", "default": "مؤسسة اَر اَر أو للمصاعد"},
            {"key": "vat_number", "label": "الرقم الضريبي:", "default": "312853107300003"},
            {"key": "invoice_total", "label": "إجمالي الفاتورة:", "default": "100.00"},
            {"key": "vat_total", "label": "قيمة الضريبة:", "default": "15.00"}
        ]
        
        for i, field in enumerate(zatca_fields):
            label = ttk.Label(fields_frame, text=field["label"], style="Heading.TLabel")
            label.grid(row=i, column=0, sticky="e", padx=(0, 10), pady=5)
            
            entry = ttk.Entry(fields_frame, style="Modern.TEntry")
            entry.insert(0, field["default"])
            entry.grid(row=i, column=1, sticky="ew", pady=5)
            
            self.zatca_entries[field["key"]] = entry
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(parent_frame)
        buttons_frame.pack(fill="x", pady=(0, 15))
        
        # زر توليد QR
        generate_qr_btn = ttk.Button(buttons_frame, text="📥 إدراج بالعقد",
                                   style="Success.TButton", command=self.generate_zatca_qr_ui)
        generate_qr_btn.pack(side="right", padx=(10, 0))
        # إضافة أحداث التمرير
        generate_qr_btn.bind("<Enter>", lambda e: self.show_element_info("زر إدراج QR", extra_info="إدراج رمز QR في العقد"))
        generate_qr_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        # زر مسح الحقول
        clear_btn = ttk.Button(buttons_frame, text="🗑️ مسح الحقول",
                             style="Secondary.TButton", command=self.clear_zatca_fields)
        clear_btn.pack(side="right")
        # إضافة أحداث التمرير
        clear_btn.bind("<Enter>", lambda e: self.show_element_info("زر مسح الحقول", extra_info="مسح جميع حقول البيانات"))
        clear_btn.bind("<Leave>", lambda e: self.clear_hover_info())
        
        # إطار عرض النتائج
        output_frame = ttk.LabelFrame(parent_frame, text="📤 النتائج", padding="10", style="Card.TFrame")
        output_frame.pack(fill="both", expand=True, pady=(0, 15))
        
        # تم إخفاء منطقة النص للـ Base64 بناء على طلب المستخدم
        
        # إطار معلومات QR
        qr_info_frame = ttk.Frame(output_frame)
        qr_info_frame.pack(fill="x")
        
        self.qr_status_label = ttk.Label(qr_info_frame, text="📍 الحالة: جاهز لتوليد QR", 
                                       style="Heading.TLabel")
        self.qr_status_label.pack(side="left")
        
        # زر فتح ملف QR
        self.open_qr_btn = ttk.Button(qr_info_frame, text="📂 فتح ملف QR", 
                                    style="Primary.TButton", command=self.open_qr_file,
                                    state="disabled")
        self.open_qr_btn.pack(side="right")

    def setup_qr_generator_tab(self):
        canvas, scrollable_frame = self.create_scrollable_page(self.qr_generator_tab)

        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))

        title_label = ttk.Label(title_frame, text="🖨️ مولد رمز الاستجابة السريعة (QR Code)", style="Title.TLabel")
        title_label.pack(anchor="center")

        # إطار حقول الإدخال مع محاذاة وسط
        input_frame = ttk.LabelFrame(scrollable_frame, text="بيانات الفاتورة", padding="20", style="Card.TFrame")
        input_frame.pack(fill="x", padx=40, pady=(0, 20), anchor="center")

        # حقول الإدخال
        fields = [
            ("اسم البائع:", "seller_name"),
            ("الرقم الضريبي:", "vat_number"),
            ("إجمالي الفاتورة:", "invoice_total"),
            ("إجمالي الضريبة:", "vat_total"),
        ]

        self.qr_entries = {}
        for i, (label_text, key) in enumerate(fields):
            label = ttk.Label(input_frame, text=label_text, style="Heading.TLabel")
            label.grid(row=i, column=0, sticky="e", padx=(0, 10), pady=5)
            entry = ttk.Entry(input_frame, style="Modern.TEntry", font=self.fonts['body'])
            entry.grid(row=i, column=1, sticky="ew", padx=(0, 5), pady=5)
            self.qr_entries[key] = entry
        
        input_frame.columnconfigure(1, weight=1)

        # زر توليد QR Code
        generate_btn = ttk.Button(scrollable_frame, text="⚙️ توليد QR Code", style="Success.TButton", command=self.generate_and_display_qr)
        generate_btn.pack(pady=10)

        # إطار عرض QR Code
        qr_display_frame = ttk.LabelFrame(scrollable_frame, text="رمز الاستجابة السريعة (QR Code)", padding="15", style="Card.TFrame")
        qr_display_frame.pack(fill="x", padx=20, pady=(0, 15))

        self.qr_image_label = tk.Label(qr_display_frame, bg="white")
        self.qr_image_label.pack(pady=10)

        self.qr_base64_label = ttk.Label(qr_display_frame, text="بيانات Base64 TLV:", style="Heading.TLabel")
        self.qr_base64_label.pack(pady=(10, 5))
        self.qr_base64_text = tk.Text(qr_display_frame, height=4, wrap="word", font=self.fonts['body'], bg="#f0f0f0", relief="flat")
        self.qr_base64_text.pack(fill="x", padx=5, pady=5)
        self.qr_base64_text.config(state="disabled")

        # زر حفظ QR Code
        save_qr_btn = ttk.Button(qr_display_frame, text="💾 حفظ QR Code", style="Primary.TButton", command=self.save_generated_qr_image)
        save_qr_btn.pack(pady=10)

    def generate_and_display_qr(self):
        seller_name = self.qr_entries["seller_name"].get().strip()
        vat_number = self.qr_entries["vat_number"].get().strip()
        invoice_total = self.qr_entries["invoice_total"].get().strip()
        vat_total = self.qr_entries["vat_total"].get().strip()

        if not all([seller_name, vat_number, invoice_total, vat_total]):
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول لتوليد QR Code.")
            return

        try:
            # توليد QR Code (zatca_generator.py سيعيد كائن الصورة مباشرة)
            qr_base64, qr_image_pil = validate_and_generate_qr(
                seller_name, vat_number, str(datetime.now().isoformat()), invoice_total, vat_total
            )

            if qr_image_pil:
                img_tk = ImageTk.PhotoImage(qr_image_pil)
                self.qr_image_label.config(image=img_tk)
                self.qr_image_label.image = img_tk  # الاحتفاظ بمرجع

                self.qr_base64_text.config(state="normal")
                self.qr_base64_text.delete(1.0, tk.END)
                self.qr_base64_text.insert(1.0, qr_base64)
                self.qr_base64_text.config(state="disabled")
                self.last_generated_qr_image_pil = qr_image_pil # تخزين كائن الصورة لزر الحفظ
            else:
                messagebox.showerror("خطأ", "فشل في توليد QR Code.")
                self.qr_image_label.config(image='')
                self.qr_image_label.image = None
                self.qr_base64_text.config(state="normal")
                self.qr_base64_text.delete(1.0, tk.END)
                self.qr_base64_text.insert(1.0, "فشل في توليد QR Code.")
                self.qr_base64_text.config(state="disabled")
                self.last_generated_qr_image_pil = None

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء توليد QR Code: {e}")
            self.qr_image_label.config(image='')
            self.qr_image_label.image = None
            self.qr_base64_text.config(state="normal")
            self.qr_base64_text.delete(1.0, tk.END)
            self.qr_base64_text.insert(1.0, f"خطأ: {e}")
            self.qr_base64_text.config(state="disabled")
            self.last_generated_qr_image_pil = None

    def save_generated_qr_image(self):
        if not hasattr(self, 'last_generated_qr_image_pil') or self.last_generated_qr_image_pil is None:
            messagebox.showwarning("تحذير", "لا يوجد QR Code لتخزينه. يرجى توليد واحد أولاً.")
            return

        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")],
            initialfile=f"ZATCA_QR_{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
        )

        if file_path:
            try:
                self.last_generated_qr_image_pil.save(file_path)
                messagebox.showinfo("نجاح", f"تم حفظ QR Code بنجاح في:\n{file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ QR Code:\n{e}")

    # --- تبويب توليد العقود ---
    def setup_contracts_tab(self):
        # إنشاء صفحة قابلة للتمرير
        canvas, scrollable_frame = self.create_scrollable_page(self.contracts_tab)

        # إطار العنوان الرئيسي مع محاذاة وسط
        title_frame = ttk.Frame(scrollable_frame)
        title_frame.pack(fill="x", padx=40, pady=(30, 20))

        title_label = ttk.Label(title_frame, text="✨ مولد العقود الذكي", style="Title.TLabel")
        title_label.pack(anchor="center")

        # إطار رئيسي مع تحسينات ومحاذاة وسط
        main_frame = ttk.Frame(scrollable_frame, padding="30")
        main_frame.pack(fill="both", expand=True, padx=40, pady=(0, 40), anchor="center")

        # إطار اختيار العميل مع تحسينات ومحاذاة وسط
        client_frame = ttk.LabelFrame(main_frame, text="👤 اختيار العميل", padding="20", style="Card.TFrame")
        client_frame.grid(row=0, column=0, padx=15, pady=(0, 20), sticky="ew")

        client_label = ttk.Label(client_frame, text="👤 اختر العميل:", style="Heading.TLabel")
        client_label.pack(anchor="center", pady=(0, 10))

        self.client_combo = ttk.Combobox(client_frame, state="readonly", style="Modern.TCombobox")
        self.client_combo.pack(fill="x", pady=(0, 5))
        self.client_combo.bind("<<ComboboxSelected>>", self.on_client_selected)
        # إضافة أحداث التمرير
        self.client_combo.bind("<Enter>", lambda e: self.show_element_info("قائمة العملاء", extra_info="اختيار العميل لإنشاء العقد"))
        self.client_combo.bind("<Leave>", lambda e: self.clear_hover_info())
        self.load_clients_combo()

        # إطار اختيار القالب مع تحسينات ومحاذاة وسط
        template_frame = ttk.LabelFrame(main_frame, text="📄 اختيار القالب", padding="20", style="Card.TFrame")
        template_frame.grid(row=0, column=1, padx=15, pady=(0, 20), sticky="ew")

        template_label = ttk.Label(template_frame, text="📄 اختر قالب العقد:", style="Heading.TLabel")
        template_label.pack(anchor="center", pady=(0, 10))

        self.template_combo = ttk.Combobox(template_frame, state="readonly", style="Modern.TCombobox")
        self.template_combo.pack(fill="x", pady=(0, 5))
        self.template_combo.bind("<<ComboboxSelected>>", self.on_template_selected)
        # إضافة أحداث التمرير
        self.template_combo.bind("<Enter>", lambda e: self.show_element_info("قائمة القوالب", extra_info="اختيار قالب العقد المطلوب"))
        self.template_combo.bind("<Leave>", lambda e: self.clear_hover_info())

        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        main_frame.grid_rowconfigure(0, weight=0)

        # إطار حقول المتغيرات مع تحسينات ومحاذاة وسط
        self.variables_frame = ttk.LabelFrame(main_frame, text="📝 تعبئة بيانات العقد", padding="20", style="Card.TFrame")
        self.variables_frame.grid(row=2, column=0, columnspan=2, padx=15, pady=(0, 20), sticky="nsew")

        # منطقة عرض حقول المتغيرات مع تحسينات بصرية
        canvas_frame = ttk.Frame(self.variables_frame)
        canvas_frame.pack(fill="both", expand=True)
        
        self.variables_canvas = tk.Canvas(canvas_frame, borderwidth=0, background=self.colors['bg_card'], highlightthickness=0)
        self.variables_canvas.pack(side="left", fill="both", expand=True)

        self.variables_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.variables_canvas.yview)
        self.variables_scrollbar.pack(side="right", fill="y")

        self.variables_canvas.configure(yscrollcommand=self.variables_scrollbar.set)
        self.variables_canvas.bind('<Configure>', lambda e: self.variables_canvas.configure(scrollregion=self.variables_canvas.bbox("all")))

        self.inner_variables_frame = ttk.Frame(self.variables_canvas)
        self.variables_canvas.create_window((0, 0), window=self.inner_variables_frame, anchor="nw")

        # تحديث حجم الإطار الداخلي عند تغيير حجم النافذة
        self.inner_variables_frame.bind("<Configure>", lambda e: self.variables_canvas.configure(scrollregion = self.variables_canvas.bbox("all")))
        
        # تحديث عرض Canvas عند تغيير حجم النافذة الرئيسية
        self.variables_canvas.bind('<Configure>', self.on_canvas_configure)
        main_frame.grid_columnconfigure(1, weight=1)

        # --- جدول الأصناف ---
        self.items_frame = ttk.LabelFrame(main_frame, text="🛒 جدول الأصناف", padding="15", style="Card.TFrame")
        self.items_frame.grid(row=4, column=0, columnspan=2, padx=10, pady=(0, 15), sticky="nsew")
        self.items_frame.grid_remove() # إخفاء الإطار افتراضياً
        
        # Configure items_frame to use grid for its internal layout
        self.items_frame.grid_columnconfigure(0, weight=1)
        self.items_frame.grid_columnconfigure(1, weight=0)
        self.items_frame.grid_columnconfigure(2, weight=1)
        self.items_frame.grid_rowconfigure(0, weight=1)

        # Treeview for items
        self.items_tree = ttk.Treeview(self.items_frame, columns=("code", "desc", "qty", "price"), show="headings", height=6)
        self.items_tree.heading("code", text="رقم الصنف")
        self.items_tree.heading("desc", text="وصف الصنف")
        self.items_tree.heading("qty", text="الكمية")
        self.items_tree.heading("price", text="السعر")
        self.items_tree.column("code", width=100, anchor="center")
        self.items_tree.column("desc", width=200, anchor="center", stretch=True)
        self.items_tree.column("qty", width=80, anchor="center")
        self.items_tree.column("price", width=100, anchor="center")
        self.items_tree.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        # Scrollbar for items_tree
        items_scrollbar = ttk.Scrollbar(self.items_frame, orient="vertical", command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        items_scrollbar.grid(row=0, column=1, sticky="ns")

        # Separator between treeview and form
        separator = ttk.Separator(self.items_frame, orient="vertical")
        separator.grid(row=0, column=1, sticky="ns", padx=5)

        # Item form panel
        self.item_form_panel = ttk.Frame(self.items_frame, style="Card.TFrame", padding="15")
        self.item_form_panel.grid(row=0, column=2, sticky="nsew", padx=(5, 0))
        self.setup_item_form_panel()
        self.hide_item_form()
        
        # قائمة الأصناف في الذاكرة
        self.items_data = []
        
        # أزرار إدارة الأصناف
        self.items_btns_frame = ttk.Frame(main_frame)
        self.items_btns_frame.grid(row=5, column=0, columnspan=2, sticky="ew", padx=10, pady=(0, 10))
        self.items_btns_frame.grid_remove() # إخفاء الإطار افتراضياً
        
        add_item_btn = ttk.Button(self.items_btns_frame, text="➕ إضافة صنف", style="Success.TButton", command=self.add_item_popup)
        add_item_btn.pack(side="right", padx=(0, 10))
        edit_item_btn = ttk.Button(self.items_btns_frame, text="✏️ تعديل صنف", style="Primary.TButton", command=self.edit_item_popup)
        edit_item_btn.pack(side="right", padx=(0, 10))
        del_item_btn = ttk.Button(self.items_btns_frame, text="🗑️ حذف صنف", style="Danger.TButton", command=self.delete_selected_item)
        del_item_btn.pack(side="right")

        # --- قسم الزكاة QR (يظهر فقط للفواتير) ---
        self.zatca_section_frame = ttk.LabelFrame(main_frame, text="🔳 مولد QR الزكاة", padding="15", style="Card.TFrame")
        self.zatca_section_frame.grid(row=6, column=0, columnspan=2, padx=10, pady=(0, 15), sticky="ew")
        self.zatca_section_frame.grid_remove() # إخفاء الإطار افتراضياً
        
        # إعداد قسم الزكاة QR داخل صفحة العقود
        self.setup_zatca_qr_section_in_contracts(self.zatca_section_frame)

        # إطار أزرار الإجراءات مع تحسينات
        action_frame = ttk.Frame(main_frame, style="Card.TFrame", padding="20")
        action_frame.grid(row=7, column=0, columnspan=2, padx=5, pady=(0, 10), sticky="ew")
        
        # عنوان قسم الإجراءات مع محاذاة وسط
        action_title = ttk.Label(action_frame, text="🚀 إجراءات العقد", style="Heading.TLabel")
        action_title.pack(anchor="center", pady=(0, 20))

        # إطار الأزرار مع محاذاة وسط
        buttons_frame = ttk.Frame(action_frame)
        buttons_frame.pack(anchor="center")

        # زر حفظ كمسودة
        draft_btn = ttk.Button(buttons_frame, text="💾 حفظ كمسودة",
                             style="Secondary.TButton", command=self.save_as_draft)
        draft_btn.pack(side="left", ipadx=20, ipady=5, padx=(0, 15))
        # إضافة أحداث التمرير
        draft_btn.bind("<Enter>", lambda e: self.show_element_info("زر حفظ كمسودة", extra_info="حفظ العقد كمسودة للتعديل لاحقاً"))
        draft_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        # زر المعاينة المباشرة
        preview_btn = ttk.Button(buttons_frame, text="👁️ معاينة العقد",
                               style="Primary.TButton", command=self.show_preview_window)
        preview_btn.pack(side="left", ipadx=20, ipady=5, padx=(0, 15))
        # إضافة أحداث التمرير
        preview_btn.bind("<Enter>", lambda e: self.show_element_info("زر معاينة العقد", extra_info="عرض العقد قبل التوليد النهائي"))
        preview_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        # زر توليد العقد
        gen_btn = ttk.Button(buttons_frame, text="⚙️ توليد العقد النهائي",
                           style="Success.TButton", command=self.generate_contract)
        gen_btn.pack(side="left", ipadx=20, ipady=5)
        # إضافة أحداث التمرير
        gen_btn.bind("<Enter>", lambda e: self.show_element_info("زر توليد العقد", extra_info="إنشاء العقد النهائي وحفظه"))
        gen_btn.bind("<Leave>", lambda e: self.clear_hover_info())

        # تحميل البيانات بعد إنشاء جميع العناصر
        self.load_templates_combo()
    
    def save_as_draft(self):
        """حفظ العقد كمسودة"""
        messagebox.showinfo("قريباً", "ميزة حفظ المسودات ستكون متاحة قريباً! 💾")
    
    def get_field_icon(self, field_name):
        """الحصول على أيقونة مناسبة للحقل حسب اسمه"""
        field_lower = field_name.lower()
        
        # أيقونات للحقول المالية
        if any(keyword in field_lower for keyword in ['قيمة', 'مبلغ', 'سعر', 'تكلفة', 'ضريبة', 'إجمالي', 'value', 'amount', 'price', 'cost', 'tax', 'total']):
            return "💰"
        
        # أيقونات للتواريخ
        elif any(keyword in field_lower for keyword in ['تاريخ', 'يوم', 'date', 'day']):
            return "📅"
        
        # أيقونات للأسماء
        elif any(keyword in field_lower for keyword in ['اسم', 'لقب', 'name', 'title']):
            return "👤"
        
        # أيقونات للمواقع
        elif any(keyword in field_lower for keyword in ['مدينة', 'منطقة', 'شارع', 'مبنى', 'معلم', 'city', 'district', 'street', 'building']):
            return "📍"
        
        # أيقونات للاتصال
        elif any(keyword in field_lower for keyword in ['هاتف', 'بريد', 'phone', 'email']):
            return "📞"
        
        # أيقونات للأرقام
        elif any(keyword in field_lower for keyword in ['رقم', 'عدد', 'number', 'count', 'tax_number', 'ضريبي']):
            return "🔢"
        
        # أيقونات للنصوص
        elif any(keyword in field_lower for keyword in ['نوع', 'وصف', 'ملاحظة', 'type', 'description', 'note']):
            return "📝"
        
        # أيقونة افتراضية
        else:
            return "📄"

    def on_canvas_configure(self, event):
        """تحديث عرض Canvas عند تغيير الحجم"""
        canvas_width = event.width
        canvas_items = self.variables_canvas.find_all()
        if canvas_items:
            self.variables_canvas.itemconfig(canvas_items[0], width=canvas_width)

    def load_clients_combo(self):
        try:
            self.cursor.execute("SELECT id, name FROM clients ORDER BY name ASC")
            clients = self.cursor.fetchall()
            self.client_combo['values'] = [client[1] for client in clients]
            self.client_data_map = {client[1]: client[0] for client in clients} # Map name to ID
            self.client_id_map = {client[0]: client[1] for client in clients} # Map ID to name
            if clients:
                self.client_combo.set(clients[0][1]) # Select first client by default
                # لا نستدعي on_client_selected هنا لأن القوالب قد لا تكون محملة بعد
            else:
                self.client_combo.set("") # Clear if no clients
                self.current_client_data = {} # Clear client data
        except sqlite3.Error as e:
            messagebox.showerror("خطأ", f"""فشل تحميل قائمة العملاء:
{e}""")

    def load_templates_combo(self):
        try:
            self.cursor.execute("SELECT id, name, file_path FROM templates ORDER BY name ASC")
            templates = self.cursor.fetchall()
            self.template_combo['values'] = [template[1] for template in templates]
            self.template_id_map = {template[1]: template[0] for template in templates} # Map name to ID
            self.template_path_map = {template[1]: template[2] for template in templates} # Map name to file_path
            if templates:
                self.template_combo.set(templates[0][1]) # Select first template by default
                self.on_template_selected()
                # الآن نستدعي on_client_selected لتعبئة البيانات إذا كان هناك عميل محدد
                if self.client_combo.get():
                    self.on_client_selected()
            else:
                self.template_combo.set("") # Clear if no templates
                self.variable_entries = {} # Clear variable entries
                self.clear_variable_fields() # Clear the display
        except sqlite3.Error as e:
            messagebox.showerror("خطأ", f"""فشل تحميل قائمة القوالب:
{e}""")

    def on_client_selected(self, event=None):
        selected_client_name = self.client_combo.get()
        if not selected_client_name:
            self.current_client_data = {}
            return

        client_id = self.client_data_map.get(selected_client_name)
        if client_id:
            try:
                self.cursor.execute("SELECT * FROM clients WHERE id = ?", (client_id,))
                client_row = self.cursor.fetchone()
                if client_row:
                    # يجب أن تكون أسماء الأعمدة متطابقة مع المتغيرات المتوقعة في القالب
                    # ID, title, name, phone, email, city, district, street, building, landmark, created_at
                    # قم بإنشاء قاموس بأسماء مفاتيح تتوافق مع متغيرات قالب الوورد
                    self.current_client_data = {
                        "id": client_row[0],
                        "title": client_row[1],
                        "name": client_row[2],
                        "phone": client_row[3],
                        "email": client_row[4],
                        "city": client_row[5],
                        "district": client_row[6],
                        "street": client_row[7],
                        "building": client_row[8],
                        "tax_number": client_row[9],
                        "address": f"{client_row[5]}, {client_row[6]}, {client_row[7]}, {client_row[8]}" if any(client_row[5:9]) else ""
                    }
                    # إضافة أسماء متغيرة عربية ومتنوعة للتوافق مع القوالب المختلفة
                    self.current_client_data["اسم_العميل"] = client_row[2]
                    self.current_client_data["هاتف_العميل"] = client_row[3]
                    self.current_client_data["بريد_العميل"] = client_row[4]
                    self.current_client_data["عنوان_العميل"] = self.current_client_data["address"]
                    self.current_client_data["الرقم_الضريبي"] = client_row[9]
                    
                    # إضافة متغيرات بأسماء مختلفة للتوافق مع القوالب
                    self.current_client_data["الاسم"] = client_row[2]
                    self.current_client_data["اللقب"] = client_row[1]
                    self.current_client_data["المدينة"] = client_row[5]
                    self.current_client_data["الهاتف"] = client_row[3]
                    self.current_client_data["البريد"] = client_row[4]
                    self.current_client_data["العنوان"] = self.current_client_data["address"]
                    
                    # متغيرات إضافية للقوالب المتقدمة
                    self.current_client_data["اسم_الشركة"] = client_row[2]  # يمكن استخدامه كاسم الشركة
                    self.current_client_data["ممثل_الشركة"] = client_row[2]
                    self.current_client_data["رقم_الهاتف"] = client_row[3]
                    self.current_client_data["البريد_الالكتروني"] = client_row[4]

                    # تحديث حقول المتغيرات إذا كان القالب محددًا
                    if hasattr(self, 'template_combo') and self.template_combo and self.template_combo.get():
                        self.update_variable_fields_with_client_data()

            except sqlite3.Error as e:
                messagebox.showerror("خطأ", f"""فشل تحميل بيانات العميل:
{e}""")
        else:
            self.current_client_data = {}

    def update_variable_fields_with_client_data(self):
        """تحديث حقول المتغيرات ببيانات العميل المحدد"""
        if not hasattr(self, 'variable_entries') or not self.variable_entries:
            return
            
        for var, entry in self.variable_entries.items():
            # مسح القيمة الحالية
            entry.delete(0, tk.END)

            # تعبئة رقم الاتفاقية تلقائياً إذا كان اسم المتغير مناسباً والقالب صيانة أو تركيب أو توريد
            if (var.strip().replace(" ", "").lower() in ["الاتفاقية", "agreement", "agreement_number"]
                and hasattr(self, 'template_combo') and self.template_combo and self.is_maintenance_or_installation_contract(self.template_combo.get())):
                entry.insert(0, str(self.get_next_agreement_number()))
                continue

            # تعبئة البيانات من العميل المحدد أو البيانات التلقائية
            filled = False

            # أولاً: البحث المباشر في بيانات العميل
            if var in self.current_client_data and self.current_client_data[var]:
                entry.insert(0, str(self.current_client_data[var]))
                filled = True

            # ثانياً: البحث بإزالة الأرقام من نهاية اسم المتغير
            elif not filled:
                # إزالة الأرقام من نهاية اسم المتغير (مثل اليوم_1 -> اليوم)
                base_var = re.sub(r'_\d+$', '', var)

                # قاموس المطابقات للمتغيرات الشائعة
                var_mappings = {
                    "اليوم": "day",
                    "التاريخ_ه": "hijri_date", 
                    "التاريخ_م": "gregorian_date",
                    "المدينة": "city",
                    "اللقب": "title",
                    "الاسم": "name",
                    "الهاتف": "phone",
                    "البريد": "email",
                    "العنوان": "address"
                }

                # البحث في المطابقات
                mapped_var = var_mappings.get(base_var, base_var)
                if mapped_var in self.current_client_data and self.current_client_data[mapped_var]:
                    entry.insert(0, str(self.current_client_data[mapped_var]))
                    filled = True

            # ثالثاً: التواريخ والبيانات التلقائية
            if not filled:
                if var.startswith("gregorian_date") or var.startswith("التاريخ_م"):
                    date_value = datetime.now().strftime("%d/%m/%Y")
                    entry.insert(0, date_value)
                    filled = True
                elif var.startswith("hijri_date") or var.startswith("التاريخ_ه"):
                    today = datetime.now()
                    hijri_date = Gregorian(today.year, today.month, today.day).to_hijri()
                    hijri_value = f"{hijri_date.day}/{hijri_date.month}/{hijri_date.year}"
                    entry.insert(0, hijri_value)
                    filled = True
                elif var.startswith("day") or var.startswith("اليوم"):
                    today = datetime.now()
                    arabic_day_names = {
                        "Saturday": "السبت", "Sunday": "الأحد", "Monday": "الإثنين", "Tuesday": "الثلاثاء",
                        "Wednesday": "الأربعاء", "Thursday": "الخميس", "Friday": "الجمعة"
                    }
                    day_value = arabic_day_names.get(today.strftime("%A"), today.strftime("%A"))
                    entry.insert(0, day_value)
                    filled = True
        
        # تحديث المعاينة إذا كانت مفتوحة
        self.update_preview_window()
        
        # تطبيق العمليات الحسابية إذا كانت هناك قيم مالية
        self.apply_calculations_if_needed()

    def bind_calculation_events(self, var, entry):
        """ربط أحداث العمليات الحسابية للحقول المالية"""
        # تحديد الحقول المالية التي تحتاج عمليات حسابية
        contract_value_fields = ["قيمة_العقد", "قيمة العقد", "contract_value", "المبلغ", "القيمة"]
        tax_value_fields = ["قيمة_الضريبة", "قيمة الضريبة", "tax_value", "الضريبة", "ضريبة"]
        total_value_fields = ["الإجمالي_بالضريبة", "الإجمالي بالضريبة", "total_with_tax", "الإجمالي", "المجموع"]
        
        # إزالة الأرقام من نهاية اسم المتغير للمقارنة
        base_var = re.sub(r'_\d+$', '', var)
        
        # ربط أحداث حسابية حسب نوع الحقل
        if any(field in base_var for field in contract_value_fields):
            # حقل قيمة العقد - حساب الضريبة تلقائياً
            entry.bind("<KeyRelease>", lambda event, v=var: self.calculate_tax_from_contract_value(v))
            entry.bind("<FocusOut>", lambda event, v=var: self.calculate_tax_from_contract_value(v))
            # إضافة تأخير قصير للتأكد من التحديث
            entry.bind("<Key>", lambda event, v=var: self.root.after(100, lambda: self.calculate_tax_from_contract_value(v)))
        elif any(field in base_var for field in total_value_fields):
            # حقل الإجمالي - لا نحتاج ربط أحداث (يُحسب تلقائياً)
            pass
        elif any(field in base_var for field in tax_value_fields):
            # حقل قيمة الضريبة - حساب الإجمالي تلقائياً
            entry.bind("<KeyRelease>", lambda event, v=var: self.calculate_total_with_tax(v))
            entry.bind("<FocusOut>", lambda event, v=var: self.calculate_total_with_tax(v))
            # إضافة تأخير قصير للتأكد من التحديث
            entry.bind("<Key>", lambda event, v=var: self.root.after(100, lambda: self.calculate_total_with_tax(v)))

    def calculate_tax_from_contract_value(self, contract_var):
        """حساب قيمة الضريبة (15%) من قيمة العقد"""
        try:
            # الحصول على قيمة العقد
            contract_entry = self.variable_entries.get(contract_var)
            if not contract_entry:
                return
                
            contract_value_str = contract_entry.get().strip()
            if not contract_value_str:
                return
                
            # تنظيف القيمة من الفواصل والرموز
            contract_value_str = self.clean_numeric_value(contract_value_str)
            contract_value = float(contract_value_str)
            
            # حساب الضريبة (15%)
            tax_value = contract_value * 0.15
            
            # البحث عن حقل قيمة الضريبة
            tax_field = self.find_tax_field()
            if tax_field and tax_field in self.variable_entries:
                tax_entry = self.variable_entries[tax_field]
                # تحديث قيمة الضريبة
                tax_entry.delete(0, tk.END)
                formatted_tax = self.format_currency(tax_value)
                tax_entry.insert(0, formatted_tax)
                
                # حساب الإجمالي تلقائياً
                self.calculate_total_with_tax(tax_field)
                self.update_qr_display() # تحديث عرض QR بعد الحساب
                
        except (ValueError, TypeError):
            # في حالة قيمة غير صالحة، لا نفعل شيء
            pass

    def calculate_total_with_tax(self, tax_var):
        """حساب الإجمالي بالضريبة (قيمة العقد + قيمة الضريبة)"""
        try:
            # البحث عن حقول قيمة العقد والضريبة
            contract_field = self.find_contract_value_field()
            tax_field = self.find_tax_field()
            total_field = self.find_total_field()
            
            if not all([contract_field, tax_field, total_field]):
                return
                
            if not all([field in self.variable_entries for field in [contract_field, tax_field, total_field]]):
                return
            
            # الحصول على القيم
            contract_value_str = self.variable_entries[contract_field].get().strip()
            tax_value_str = self.variable_entries[tax_field].get().strip()
            
            if not contract_value_str or not tax_value_str:
                return
            
            # تنظيف القيم
            contract_value_str = self.clean_numeric_value(contract_value_str)
            tax_value_str = self.clean_numeric_value(tax_value_str)
            
            contract_value = float(contract_value_str)
            tax_value = float(tax_value_str)
            
            # حساب الإجمالي
            total_value = contract_value + tax_value
            
            # تحديث حقل الإجمالي
            # تحديث حقل الإجمالي
            total_entry = self.variable_entries[total_field]
            total_entry.delete(0, tk.END)
            total_entry.insert(0, self.format_currency(total_value))
            self.update_qr_display() # تحديث عرض QR بعد الحساب
            
        except (ValueError, TypeError):
            # في حالة قيمة غير صالحة، لا نفعل شيء
            pass

    def find_contract_value_field(self):
        """البحث عن حقل قيمة العقد"""
        contract_keywords = ["قيمة_العقد", "قيمة العقد", "contract_value", "المبلغ", "القيمة"]
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if any(keyword in base_var for keyword in contract_keywords):
                return var
        return None

    def find_tax_field(self):
        """البحث عن حقل قيمة الضريبة"""
        # البحث عن حقل الضريبة أولاً
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if base_var == "الضريبة":
                return var
        
        # البحث عن حقول الضريبة الأخرى
        tax_keywords = ["قيمة_الضريبة", "قيمة الضريبة", "tax_value", "ضريبة"]
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if any(keyword in base_var for keyword in tax_keywords):
                return var
        return None

    def find_total_field(self):
        """البحث عن حقل الإجمالي بالضريبة"""
        # البحث عن حقل total أولاً (الإجمالي الفرعي بدون ضريبة)
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if base_var == "total":
                return var
        
        # إذا لم يوجد total، ابحث عن الحقول الأخرى
        total_keywords = ["الإجمالي_بالضريبة", "الإجمالي بالضريبة", "total_with_tax", "اجمالي", "الإجمالي", "المجموع"]
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if any(keyword in base_var for keyword in total_keywords):
                return var
        return None
    
    def find_subtotal_field(self):
        """البحث عن حقول الإجمالي البديلة"""
        subtotal_keywords = [
            "المجموع_الفرعي", "المجموع الفرعي", "subtotal", 
            "إجمالي_الأصناف", "إجمالي الأصناف", "items_total",
            "المبلغ_الإجمالي", "المبلغ الإجمالي", "total_amount",
            "قيمة_الأصناف", "قيمة الأصناف", "items_value",
            "total", "مجموع", "إجمالي"
        ]
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if any(keyword in base_var for keyword in subtotal_keywords):
                return var
        return None
    
    def find_final_total_field(self):
        """البحث عن حقل الإجمالي النهائي"""
        # البحث عن حقل اجمالي أولاً
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if base_var == "اجمالي":
                return var
        
        # البحث عن حقول الإجمالي النهائي الأخرى
        final_total_keywords = ["الإجمالي_النهائي", "الإجمالي النهائي", "final_total", "grand_total", "المجموع_الكلي"]
        for var in self.variable_entries.keys():
            base_var = re.sub(r'_\d+$', '', var)
            if any(keyword in base_var for keyword in final_total_keywords):
                return var
        return None

    def apply_calculations_if_needed(self):
        """تطبيق العمليات الحسابية إذا كانت هناك قيم مالية"""
        # البحث عن حقل قيمة العقد وتطبيق الحسابات إذا كان له قيمة
        contract_field = self.find_contract_value_field()
        if contract_field and contract_field in self.variable_entries:
            contract_value = self.variable_entries[contract_field].get().strip()
            if contract_value:
                # تطبيق حساب الضريبة
                self.calculate_tax_from_contract_value(contract_field)

    def clean_numeric_value(self, value_str):
        """تنظيف القيمة الرقمية من الرموز والفواصل"""
        if not value_str:
            return "0"
        
        # إزالة الرموز والكلمات الشائعة
        cleaned = value_str.replace(',', '').replace('،', '')  # فواصل عربية وإنجليزية
        cleaned = cleaned.replace('ريال', '').replace('SR', '').replace('SAR', '')
        cleaned = cleaned.replace('$', '').replace('€', '').replace('£', '')
        cleaned = cleaned.strip()
        
        # تحويل الأرقام العربية إلى إنجليزية
        arabic_to_english = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        }
        
        for arabic, english in arabic_to_english.items():
            cleaned = cleaned.replace(arabic, english)
        
        # إزالة أي أحرف غير رقمية عدا النقطة العشرية
        import re
        cleaned = re.sub(r'[^\d.]', '', cleaned)
        
        # التأكد من وجود رقم صالح
        if not cleaned or cleaned == '.':
            return "0"
            
        return cleaned

    def format_currency(self, value):
        """تنسيق القيمة المالية بدون فواصل الآلاف"""
        try:
            # تنسيق الرقم بدون فواصل الآلاف ومع منزلتين عشريتين
            formatted = f"{value:.2f}"
            # إزالة الأصفار الزائدة من النهاية
            if formatted.endswith('.00'):
                formatted = formatted[:-3]
            return formatted
        except:
            return str(value)

    def clear_variable_fields(self):
        # مسح جميع widgets من inner_variables_frame
        for widget in self.inner_variables_frame.winfo_children():
            widget.destroy()
        self.variable_entries = {} # مسح قاموس حقول الإدخال

    def is_valid_variable_name(self, var_name):
        """التحقق من صحة اسم المتغير"""
        try:
            # التحقق من أن المتغير لا يبدأ برقم
            if var_name and var_name[0].isdigit():
                return False
            
            # التحقق من أن المتغير يحتوي على محارف صالحة فقط
            # يُسمح بالحروف والأرقام والشرطة السفلية والحروف العربية
            import re
            if not re.match(r'^[a-zA-Z_\u0600-\u06FF][a-zA-Z0-9_\u0600-\u06FF]*$', var_name):
                return False
            
            # التحقق من أن المتغير ليس كلمة محجوزة في Python
            python_keywords = ['and', 'as', 'assert', 'break', 'class', 'continue', 'def', 
                             'del', 'elif', 'else', 'except', 'exec', 'finally', 'for', 
                             'from', 'global', 'if', 'import', 'in', 'is', 'lambda', 
                             'not', 'or', 'pass', 'print', 'raise', 'return', 'try', 
                             'while', 'with', 'yield']
            if var_name.lower() in python_keywords:
                return False
                
            return True
        except:
            return False

    def on_template_selected(self, event=None):
        self.clear_variable_fields() # مسح الحقول القديمة أولاً

        selected_template_name = self.template_combo.get()
        if not selected_template_name:
            return

        file_path = self.template_path_map.get(selected_template_name)
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("خطأ", "مسار القالب غير صالح أو الملف غير موجود.")
            return

        try:
            doc = DocxTemplate(file_path)
            # استخراج جميع المتغيرات، وتصفية المتغيرات الداخلية (مثل __loop__) أو التي تحتوي على '.'
            all_vars = doc.undeclared_template_variables
            # تصفية المتغيرات التي لا تبدأ بـ __
            # تصفية المتغيرات التي لا تبدأ بـ __ وتلك التي يجب أن تُحسب تلقائيًا أو تُدار بواسطة واجهة الأصناف
            excluded_vars = ['items', 'qr_code_image'] # Add 'items' for explicit clarity, though it's already handled by UI
            
            # تنظيف المتغيرات من الأسماء غير الصالحة
            cleaned_vars = []
            invalid_vars = []
            for v in all_vars:
                if not v.startswith('__') and v not in excluded_vars:
                    # التحقق من صحة اسم المتغير
                    if self.is_valid_variable_name(v):
                        cleaned_vars.append(v)
                    else:
                        invalid_vars.append(v)
                        print(f"تحذير: تم تجاهل المتغير غير الصالح: {v}")
            
            # إظهار تحذير للمستخدم إذا كانت هناك متغيرات غير صالحة
            if invalid_vars:
                invalid_list = '\n'.join([f"• {var}" for var in invalid_vars])
                messagebox.showwarning("متغيرات غير صالحة", 
                    f"""تم تجاهل المتغيرات التالية لأنها غير صالحة:

{invalid_list}

نصائح لأسماء المتغيرات الصحيحة:
• لا تبدأ بأرقام (مثل: 15_الضريبة ❌)
• ابدأ بحرف أو شرطة سفلية (مثل: الضريبة_15 ✅)
• استخدم الحروف والأرقام والشرطة السفلية فقط
• تجنب الكلمات المحجوزة في البرمجة""")
            
            filtered_vars = cleaned_vars

            # فرز المتغيرات: أولاً، فرز حسب الأرقام إذا كانت موجودة (مثال: field_1, field_2), ثم أبجديًا
            def sort_key(var_name):
                match = re.search(r'_(\d+)$', var_name)
                if match:
                    return (0, int(match.group(1)), var_name) # الأولوية للأرقام
                return (1, 0, var_name) # ثم أبجديًا

            vars_list = sorted(filtered_vars, key=sort_key)

            # إضافة حقول المتغيرات ديناميكيًا مع تحسينات بصرية
            for i, var in enumerate(vars_list):
                # إخفاء الحقول من الواجهة إذا احتوى اسم المتغير أو اسمه الأساسي أو عرضه على كلمات ضريبة/اجمال/total
                base_var = re.sub(r'_\d+$', '', var).lower()
                display_var = re.sub(r'_\d+$', '', var).replace('_', ' ').strip().lower()
                if any(keyword in base_var or keyword in display_var for keyword in ['ضريبة', 'اجمال', 'total']):
                    # أنشئ عنصر Entry غير ظاهر (بدون إضافته للواجهة)
                    hidden_entry = ttk.Entry(self.inner_variables_frame)
                    self.variable_entries[var] = hidden_entry
                    continue

                # تنظيف اسم المتغير للعرض مع أيقونات
                display_var_show = re.sub(r'_\d+$', '', var).replace('_', ' ').strip()
                icon = self.get_field_icon(var)
                label_text = f"{icon} {display_var_show}:"

                # إنشاء إطار للحقل مع تحسينات
                field_frame = ttk.Frame(self.inner_variables_frame, style="Card.TFrame", padding="8")
                field_frame.grid(row=i, column=0, columnspan=2, sticky="ew", pady=5, padx=10)
                field_frame.columnconfigure(1, weight=1)

                # التسمية مع تحسينات
                label = ttk.Label(field_frame, text=label_text, style="Heading.TLabel")
                label.grid(row=0, column=0, sticky="e", padx=(0, 10))

                # حقل الإدخال مع تحسينات
                entry = ttk.Entry(field_frame, style="Modern.TEntry", font=self.fonts['body'])
                entry.grid(row=0, column=1, sticky="ew", padx=(0, 5))
                self.variable_entries[var] = entry

                # محاولة ملء البيانات من العميل المحدد أو البيانات التلقائية
                if var in self.current_client_data:
                    entry.insert(0, self.current_client_data[var])
                elif var == "gregorian_date" or var == "التاريخ_م":
                    entry.insert(0, datetime.now().strftime("%d/%m/%Y"))
                elif var == "hijri_date" or var == "التاريخ_ه":
                    today = datetime.now()
                    hijri_date = Gregorian(today.year, today.month, today.day).to_hijri()
                    entry.insert(0, f"{hijri_date.day}/{hijri_date.month}/{hijri_date.year}")
                elif var == "day" or var == "اليوم":
                    today = datetime.now()
                    arabic_day_names = {
                        "Saturday": "السبت", "Sunday": "الأحد", "Monday": "الإثنين", "Tuesday": "الثلاثاء",
                        "Wednesday": "الأربعاء", "Thursday": "الخميس", "Friday": "الجمعة"
                    }
                    entry.insert(0, arabic_day_names.get(today.strftime("%A"), today.strftime("%A")))

                # ربط أحداث العمليات الحسابية والمعاينة
                self.bind_calculation_events(var, entry)

                # ربط حدث لتوليد المعاينة عند تغيير حقل الإدخال
                entry.bind("<FocusOut>", lambda event: self.update_preview_window())
                entry.bind("<KeyRelease>", lambda event: self.update_preview_window())

                # ربط حقول الزكاة بتحديث عرض QR
                if var in ["seller_name", "vat_number", self.find_total_field(), self.find_tax_field()]:
                    entry.bind("<KeyRelease>", lambda event: self.update_qr_display())
                    entry.bind("<FocusOut>", lambda event: self.update_qr_display())
            
            # تحديث عرض Canvas بعد إضافة الـ widgets
            self.inner_variables_frame.update_idletasks()
            self.variables_canvas.configure(scrollregion=self.variables_canvas.bbox("all"))
            
            # تحديث عرض الإطار الداخلي
            self.inner_variables_frame.grid_columnconfigure(1, weight=1)
            
            # تحديث الإجمالي من الأصناف إذا كانت موجودة
            if hasattr(self, 'items_data') and self.items_data:
                self.root.after(100, self.update_total_from_items)

        # تفعيل/تعطيل واجهة الأصناف بناءً على حالة القالب
            is_invoice = self.is_invoice_template(selected_template_name)
            has_items_table = self.template_has_items_table.get(selected_template_name, False)
            
            if has_items_table or is_invoice:
                self.items_frame.grid()
                self.items_btns_frame.grid()
                self.refresh_items_treeview() # تحديث عرض الأصناف عند تفعيل الواجهة
                
                # إظهار قسم الزكاة QR للفواتير
                if is_invoice:
                    if hasattr(self, 'zatca_section_frame'):
                        self.zatca_section_frame.grid()
                        # تحميل الإعدادات المحفوظة تلقائياً
                        self.root.after(100, self.load_zatca_settings_silently)
                        # إظهار رسالة توضيحية
                        messagebox.showinfo("🔳 قسم الزكاة QR", 
                            f"تم تفعيل قسم مولد QR الزكاة لأن القالب '{selected_template_name}' تم اكتشافه كفاتورة.\n\nيمكنك الآن استخدام مولد QR الزكاة أسفل جدول الأصناف.")
                else:
                    if hasattr(self, 'zatca_section_frame'):
                        self.zatca_section_frame.grid_remove()
            else:
                self.items_frame.grid_remove()
                if hasattr(self, 'zatca_section_frame'):
                    self.zatca_section_frame.grid_remove()  # إخفاء قسم الزكاة QR
                self.items_btns_frame.grid_remove()
                self.items_data = [] # مسح بيانات الأصناف إذا كان القالب لا يدعمها
                self.refresh_items_treeview()
                
                # رسالة توضيحية للمستخدم مع خيار تفعيل يدوي
                if not is_invoice and not has_items_table:
                    # تم تعطيل الرسالة المنبثقة بناءً على طلب المستخدم
                    pass

        except Exception as e:
            error_msg = str(e)
            
            # تحسين رسائل الخطأ الشائعة
            if "expected token 'end of print statement" in error_msg:
                error_msg = """خطأ في صيغة المتغيرات داخل القالب.

المشكلة الشائعة:
• متغير يبدأ برقم (مثل: {{15_الضريبة}})
• استخدام محارف غير صالحة في أسماء المتغيرات

الحل:
• غيّر أسماء المتغيرات لتبدأ بحرف (مثل: {{الضريبة_15}})
• استخدم الحروف والأرقام والشرطة السفلية فقط
• تأكد من استخدام {{ }} حول المتغيرات"""
            elif "unexpected char" in error_msg:
                error_msg = """خطأ في تنسيق القالب.

تأكد من:
• استخدام {{ }} حول المتغيرات بشكل صحيح
• عدم وجود محارف خاصة غير مدعومة
• صحة تنسيق الجداول والحلقات"""
            
            messagebox.showerror("خطأ في القالب", f"""فشل في قراءة القالب أو إنشاء حقول المتغيرات:

{error_msg}

اسم الملف: {selected_template_name}""")
            self.clear_variable_fields() # مسح أي حقول خاطئة

    def show_preview_window(self):
        """إظهار نافذة المعاينة الداخلية"""
        if self.preview_window is None or not self.preview_window.winfo_exists():
            self.create_preview_window()
        else:
            self.preview_window.lift()  # رفع النافذة إلى المقدمة
        
        self.update_preview_content()

    def create_preview_window(self):
        """إنشاء نافذة المعاينة"""
        self.preview_window = tk.Toplevel(self.root)
        self.preview_window.title("معاينة العقد المباشرة - طبق الأصل")
        self.preview_window.geometry("900x700")
        self.preview_window.transient(self.root)
        
        # إطار للأزرار
        btn_frame = ttk.Frame(self.preview_window, padding="10")
        btn_frame.pack(fill="x")
        
        refresh_btn = tk.Button(btn_frame, text="🔄 تحديث", command=self.update_preview_content, 
                               font=("Arial", 10), bg="#28a745", fg="black")
        refresh_btn.pack(side="right", padx=5)
        
        save_btn = tk.Button(btn_frame, text="💾 حفظ كـ Word", command=self.generate_preview_contract, 
                            font=("Arial", 10), bg="#007bff", fg="black")
        save_btn.pack(side="right", padx=5)
        
        open_browser_btn = tk.Button(btn_frame, text="🌐 فتح في المتصفح", command=self.open_preview_in_browser, 
                                    font=("Arial", 10), bg="#6c757d", fg="black")
        open_browser_btn.pack(side="right", padx=5)
        
        # زر فتح في Word (إذا كان متاحاً)
        if WIN32COM_AVAILABLE:
            word_btn = tk.Button(btn_frame, text="📄 فتح في Word", command=self.open_preview_in_word, 
                                font=("Arial", 10), bg="#0078d4", fg="black")
            word_btn.pack(side="right", padx=5)
        
        # زر تحويل إلى PDF (إذا كان متاحاً)
        if DOCX2PDF_AVAILABLE:
            pdf_btn = tk.Button(btn_frame, text="📑 معاينة PDF", command=self.show_pdf_preview, 
                               font=("Arial", 10), bg="#dc3545", fg="black")
            pdf_btn.pack(side="right", padx=5)
        
        # تبويبات للمعاينة
        notebook = ttk.Notebook(self.preview_window)
        notebook.pack(fill="both", expand=True, padx=10, pady=5)
        
        # تبويب المعاينة المنسقة (HTML)
        html_frame = ttk.Frame(notebook)
        notebook.add(html_frame, text="📄 معاينة منسقة")
        
        try:
            # محاولة استخدام HTMLScrolledText
            self.preview_html = HTMLScrolledText(html_frame, html="<p style='text-align: center; color: black;'>جاري التحميل...</p>")
            self.preview_html.pack(fill="both", expand=True, padx=5, pady=5)
            self.html_widget_type = "html"
        except Exception as e:
            # في حالة فشل HTMLScrolledText، استخدم Text عادي مع رسالة توضيحية
            self.preview_html = tk.Text(html_frame, wrap=tk.WORD, font=("Arial", 12), 
                                      bg="#f8f9fa", fg="black", relief="sunken", bd=2)
            self.preview_html.pack(fill="both", expand=True, padx=5, pady=5)
            self.preview_html.insert(1.0, "المعاينة المنسقة غير متاحة.\nاستخدم 'فتح في المتصفح' للمعاينة الكاملة.")
            self.html_widget_type = "text"
            print(f"تحذير: فشل في تحميل HTMLScrolledText: {e}")
            
        # إضافة شريط تمرير للمعاينة المنسقة
        if self.html_widget_type == "text":
            html_scrollbar = ttk.Scrollbar(html_frame, orient="vertical", command=self.preview_html.yview)
            html_scrollbar.pack(side="right", fill="y")
            self.preview_html.configure(yscrollcommand=html_scrollbar.set)
        
        # تبويب المعاينة النصية
        text_frame = ttk.Frame(notebook)
        notebook.add(text_frame, text="📝 معاينة نصية")
        
        self.preview_text = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 12), 
                                   bg="#f8f9fa", fg="black", relief="sunken", bd=2)
        self.preview_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # شريط التمرير للنص
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.preview_text.yview)
        scrollbar.pack(side="right", fill="y")
        self.preview_text.configure(yscrollcommand=scrollbar.set)
        
        # متغير لحفظ مسار ملف HTML المؤقت
        self.temp_html_file = None

    def update_preview_window(self):
        """تحديث نافذة المعاينة إذا كانت مفتوحة"""
        if self.preview_window and self.preview_window.winfo_exists():
            self.update_preview_content()

    def update_preview_content(self):
        """تحديث محتوى المعاينة"""
        if not hasattr(self, 'preview_text') or not self.preview_text:
            return
            
        client_name = self.client_combo.get()
        template_name = self.template_combo.get()

        if not client_name or not template_name:
            # تحديث المعاينة النصية
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, "يرجى اختيار عميل وقالب لعرض المعاينة")
            
            # تحديث المعاينة المنسقة
            if hasattr(self, 'preview_html'):
                try:
                    if hasattr(self, 'html_widget_type') and self.html_widget_type == "html":
                        self.preview_html.set_html("<p style='text-align: center; color: #666;'>يرجى اختيار عميل وقالب لعرض المعاينة</p>")
                    else:
                        self.preview_html.delete(1.0, tk.END)
                        self.preview_html.insert(1.0, "يرجى اختيار عميل وقالب لعرض المعاينة")
                except:
                    pass
            return

        # جمع البيانات
        data = {}
        for var, entry in self.variable_entries.items():
            data[var] = entry.get()

        file_path = self.template_path_map.get(template_name)
        if not file_path or not os.path.exists(file_path):
            error_msg = "خطأ: ملف القالب غير موجود"
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, error_msg)
            
            if hasattr(self, 'preview_html'):
                try:
                    if hasattr(self, 'html_widget_type') and self.html_widget_type == "html":
                        self.preview_html.set_html(f"<p style='color: red;'>{error_msg}</p>")
                    else:
                        self.preview_html.delete(1.0, tk.END)
                        self.preview_html.insert(1.0, error_msg)
                except:
                    pass
            return

        try:
            # دمج البيانات
            context_data = {**self.current_client_data, **data}
            
            # إضافة بيانات التاريخ التلقائية
            today = datetime.now()
            gregorian_date_str = today.strftime("%d/%m/%Y")
            hijri_date = Gregorian(today.year, today.month, today.day).to_hijri()
            hijri_date_str = f"{hijri_date.day}/{hijri_date.month}/{hijri_date.year}"
            day_name = today.strftime("%A")
            arabic_day_names = {
                "Saturday": "السبت", "Sunday": "الأحد", "Monday": "الإثنين", "Tuesday": "الثلاثاء",
                "Wednesday": "الأربعاء", "Thursday": "الخميس", "Friday": "الجمعة"
            }
            day_name_arabic = arabic_day_names.get(day_name, day_name)

            if "التاريخ_م" not in context_data or not context_data["التاريخ_م"].strip():
                context_data["التاريخ_م"] = gregorian_date_str
            if "gregorian_date" not in context_data or not context_data["gregorian_date"].strip():
                context_data["gregorian_date"] = gregorian_date_str
            if "التاريخ_ه" not in context_data or not context_data["التاريخ_ه"].strip():
                context_data["التاريخ_ه"] = hijri_date_str
            if "hijri_date" not in context_data or not context_data["hijri_date"].strip():
                context_data["hijri_date"] = hijri_date_str
            if "اليوم" not in context_data or not context_data["اليوم"].strip():
                context_data["اليوم"] = day_name_arabic
            if "day" not in context_data or not context_data["day"].strip():
                context_data["day"] = day_name_arabic

            # إنشاء ملف Word مؤقت مع البيانات (يتضمن QR Code الآن)
            temp_docx_path = self.create_current_preview_docx()
            if not temp_docx_path:
                error_msg = "خطأ: فشل في إنشاء ملف المعاينة المؤقت."
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(1.0, error_msg)
                if hasattr(self, 'preview_html'):
                    try:
                        if hasattr(self, 'html_widget_type') and self.html_widget_type == "html":
                            self.preview_html.set_html(f"<p style='color: red;'>{error_msg}</p>")
                        else:
                            self.preview_html.delete(1.0, tk.END)
                            self.preview_html.insert(1.0, error_msg)
                    except:
                        pass
                return
            
            # تحويل Word إلى HTML للمعاينة المنسقة
            html_content = self.convert_docx_to_html(temp_docx_path)
            
            # تحديث المعاينة المنسقة
            if hasattr(self, 'preview_html') and html_content:
                try:
                    if hasattr(self, 'html_widget_type') and self.html_widget_type == "html":
                        # استخدام HTMLScrolledText
                        self.preview_html.set_html(html_content)
                    else:
                        # استخدام Text عادي - عرض نص مبسط بدلاً من HTML
                        self.preview_html.delete(1.0, tk.END)
                        # تحويل HTML إلى نص مبسط
                        import re
                        # إزالة علامات HTML
                        clean_text = re.sub('<[^<]+?>', '', html_content)
                        # تنظيف النص
                        clean_text = clean_text.replace('&nbsp;', ' ').replace('&amp;', '&')
                        clean_text = clean_text.replace('&lt;', '<').replace('&gt;', '>')
                        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
                        
                        if clean_text:
                            self.preview_html.insert(1.0, clean_text)
                        else:
                            self.preview_html.insert(1.0, "المعاينة المنسقة غير متاحة.\nاستخدم 'فتح في المتصفح' للمعاينة الكاملة.")
                except Exception as e:
                    print(f"خطأ في عرض HTML: {e}")
                    # في حالة الفشل، عرض رسالة خطأ
                    if hasattr(self.preview_html, 'delete'):
                        self.preview_html.delete(1.0, tk.END)
                        self.preview_html.insert(1.0, f"خطأ في المعاينة: {str(e)}\nاستخدم 'فتح في المتصفح' للمعاينة الكاملة.")

            # إنشاء المعاينة النصية
            preview_content = f"العميل: {client_name}\n"
            preview_content += f"القالب: {template_name}\n"
            preview_content += f"التاريخ الميلادي: {gregorian_date_str}\n"
            preview_content += f"التاريخ الهجري: {hijri_date_str}\n"
            preview_content += f"اليوم: {day_name_arabic}\n"
            preview_content += "\n" + "="*50 + "\n\n"
            
            # إضافة المتغيرات وقيمها
            preview_content += "المتغيرات المستخدمة:\n\n"
            for var, value in context_data.items():
                if value:  # عرض المتغيرات التي لها قيم فقط
                    preview_content += f"{var}: {value}\n"
            
            preview_content += "\n" + "="*50 + "\n\n"
            preview_content += "ملاحظة: للمعاينة المنسقة الكاملة، انتقل إلى تبويب 'معاينة منسقة'\n"
            preview_content += "أو استخدم زر 'فتح في المتصفح' لرؤية التنسيق الكامل."

            # تحديث المعاينة النصية
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, preview_content)
            
            # حفظ مسار ملف HTML المؤقت
            if html_content:
                self.save_temp_html_file(html_content)
            
        except Exception as e:
            error_msg = f"خطأ في إنشاء المعاينة:\n{str(e)}"
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, error_msg)
            
            if hasattr(self, 'preview_html'):
                try:
                    if hasattr(self, 'html_widget_type') and self.html_widget_type == "html":
                        self.preview_html.set_html(f"<p style='color: red;'>{error_msg}</p>")
                    else:
                        self.preview_html.delete(1.0, tk.END)
                        self.preview_html.insert(1.0, error_msg)
                except:
                    pass

    def create_temp_docx_with_data(self, doc_object, context_data):
        """إنشاء ملف Word مؤقت مع البيانات المدخلة"""
        try:
            doc_object.render(context_data)
            
            # إنشاء ملف مؤقت
            temp_dir = tempfile.gettempdir()
            temp_docx_path = os.path.join(temp_dir, f"preview_temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx")
            doc_object.save(temp_docx_path)
            
            # إضافة الملف إلى قائمة التنظيف
            self.temp_files_to_cleanup.append(temp_docx_path)
            
            return temp_docx_path
        except Exception as e:
            print(f"خطأ في إنشاء ملف Word المؤقت: {e}")
            return None

    def convert_docx_to_html(self, docx_path):
        """تحويل ملف Word إلى HTML باستخدام mammoth"""
        if not docx_path or not os.path.exists(docx_path):
            return None
            
        try:
            with open(docx_path, "rb") as docx_file:
                result = mammoth.convert_to_html(docx_file)
                html_content = result.value
                
                # إضافة CSS محسن للتنسيق العربي وطبق الأصل
                styled_html = f"""
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>معاينة العقد</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap');
                        
                        body {{
                            font-family: 'Cairo', 'Amiri', 'Arial', 'Tahoma', sans-serif;
                            direction: rtl;
                            text-align: right;
                            line-height: 1.8;
                            margin: 40px;
                            background-color: #ffffff;
                            color: #000000;
                            font-size: 14px;
                            max-width: 210mm;
                            min-height: 297mm;
                            box-shadow: 0 0 10px rgba(0,0,0,0.1);
                            padding: 40px;
                        }}
                        
                        h1 {{
                            font-family: 'Amiri', serif;
                            font-size: 24px;
                            font-weight: 700;
                            text-align: center;
                            color: #1a1a1a;
                            margin: 20px 0;
                            border-bottom: 2px solid #333;
                            padding-bottom: 10px;
                        }}
                        
                        h2 {{
                            font-family: 'Cairo', sans-serif;
                            font-size: 18px;
                            font-weight: 600;
                            color: #2c3e50;
                            margin: 15px 0 10px 0;
                            border-right: 4px solid #3498db;
                            padding-right: 10px;
                        }}
                        
                        h3, h4, h5, h6 {{
                            font-family: 'Cairo', sans-serif;
                            color: #34495e;
                            margin: 12px 0 8px 0;
                        }}
                        
                        p {{
                            margin: 8px 0;
                            text-align: justify;
                            font-size: 14px;
                            line-height: 1.8;
                        }}
                        
                        table {{
                            width: 100%;
                            border-collapse: collapse;
                            margin: 15px 0;
                            font-size: 13px;
                            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                        }}
                        
                        th {{
                            background-color: #f8f9fa;
                            border: 1px solid #dee2e6;
                            padding: 12px 8px;
                            text-align: center;
                            font-weight: 600;
                            color: #495057;
                        }}
                        
                        td {{
                            border: 1px solid #dee2e6;
                            padding: 10px 8px;
                            text-align: right;
                            vertical-align: top;
                        }}
                        
                        tr:nth-child(even) {{
                            background-color: #f8f9fa;
                        }}
                        
                        .center {{
                            text-align: center;
                        }}
                        
                        .bold {{
                            font-weight: 600;
                        }}
                        
                        .underline {{
                            text-decoration: underline;
                        }}
                        
                        .signature-line {{
                            border-bottom: 1px solid #000;
                            display: inline-block;
                            min-width: 200px;
                            margin: 0 10px;
                        }}
                        
                        ul, ol {{
                            margin: 10px 0;
                            padding-right: 20px;
                        }}
                        
                        li {{
                            margin: 5px 0;
                            line-height: 1.6;
                        }}
                        
                        .page-break {{
                            page-break-before: always;
                        }}
                        
                        @media print {{
                            body {{
                                margin: 0;
                                box-shadow: none;
                                max-width: none;
                            }}
                        }}
                    </style>
                </head>
                <body>
                    {html_content}
                </body>
                </html>
                """
                
                return styled_html
                
        except Exception as e:
            print(f"خطأ في تحويل Word إلى HTML: {e}")
            return f"<p style='color: red;'>خطأ في تحويل الملف: {str(e)}</p>"

    def save_temp_html_file(self, html_content):
        """حفظ ملف HTML مؤقت للفتح في المتصفح"""
        try:
            temp_dir = tempfile.gettempdir()
            self.temp_html_file = os.path.join(temp_dir, f"contract_preview_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
            
            with open(self.temp_html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
        except Exception as e:
            print(f"خطأ في حفظ ملف HTML: {e}")
            self.temp_html_file = None

    def open_preview_in_browser(self):
        """فتح المعاينة في المتصفح"""
        if self.temp_html_file and os.path.exists(self.temp_html_file):
            try:
                webbrowser.open(f'file://{self.temp_html_file}')
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح المتصفح:\n{e}")
        else:
            messagebox.showwarning("تحذير", "لا يوجد ملف معاينة متاح. يرجى تحديث المعاينة أولاً.")

    def open_preview_in_word(self):
        """فتح المعاينة في Microsoft Word مباشرة"""
        if not WIN32COM_AVAILABLE:
            messagebox.showerror("خطأ", "Microsoft Word غير متاح على هذا النظام")
            return
            
        try:
            # إنشاء ملف Word مؤقت مع البيانات
            temp_docx_path = self.create_current_preview_docx()
            if not temp_docx_path:
                messagebox.showerror("خطأ", "فشل في إنشاء ملف المعاينة")
                return
            
            # فتح Word باستخدام COM
            word_app = win32com.client.Dispatch("Word.Application")
            word_app.Visible = True
            doc = word_app.Documents.Open(temp_docx_path)
            
            # جعل المستند للقراءة فقط
            doc.Protect(Type=2)  # wdAllowOnlyReading
            
            messagebox.showinfo("معاينة Word", "تم فتح المعاينة في Microsoft Word\nالملف محمي للقراءة فقط")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح Word:\n{str(e)}")

    def show_pdf_preview(self):
        """تحويل إلى PDF وعرض المعاينة"""
        if not DOCX2PDF_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة تحويل PDF غير متاحة")
            return
            
        try:
            # إنشاء ملف Word مؤقت مع البيانات
            temp_docx_path = self.create_current_preview_docx()
            if not temp_docx_path:
                messagebox.showerror("خطأ", "فشل في إنشاء ملف المعاينة")
                return
            
            # تحويل إلى PDF
            temp_dir = tempfile.gettempdir()
            pdf_path = os.path.join(temp_dir, f"contract_preview_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
            
            messagebox.showinfo("جاري التحويل", "جاري تحويل الملف إلى PDF...\nقد يستغرق بضع ثوانٍ")
            
            convert(temp_docx_path, pdf_path)
            self.temp_files_to_cleanup.append(pdf_path)
            
            # فتح PDF
            self.open_file(pdf_path)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحويل إلى PDF:\n{str(e)}")

    def create_current_preview_docx(self):
        """إنشاء ملف Word مؤقت بالبيانات الحالية للمعاينة"""
        client_name = self.client_combo.get()
        template_name = self.template_combo.get()

        if not client_name or not template_name:
            return None

        # جمع البيانات
        data = {}
        for var, entry in self.variable_entries.items():
            data[var] = entry.get()

        file_path = self.template_path_map.get(template_name)
        if not file_path or not os.path.exists(file_path):
            return None

        qr_code_temp_file = None
        try:
            # دمج البيانات
            context_data = {**self.current_client_data, **data}
            
            # إضافة بيانات التاريخ التلقائية
            today = datetime.now()
            gregorian_date_str = today.strftime("%d/%m/%Y")
            hijri_date = Gregorian(today.year, today.month, today.day).to_hijri()
            hijri_date_str = f"{hijri_date.day}/{hijri_date.month}/{hijri_date.year}"
            day_name = today.strftime("%A")
            arabic_day_names = {
                "Saturday": "السبت", "Sunday": "الأحد", "Monday": "الإثنين", "Tuesday": "الثلاثاء",
                "Wednesday": "الأربعاء", "Thursday": "الخميس", "Friday": "الجمعة"
            }
            day_name_arabic = arabic_day_names.get(day_name, day_name)

            if "التاريخ_م" not in context_data or not context_data["التاريخ_م"].strip():
                context_data["التاريخ_م"] = gregorian_date_str
            if "gregorian_date" not in context_data or not context_data["gregorian_date"].strip():
                context_data["gregorian_date"] = gregorian_date_str
            if "التاريخ_ه" not in context_data or not context_data["التاريخ_ه"].strip():
                context_data["التاريخ_ه"] = hijri_date_str
            if "hijri_date" not in context_data or not context_data["hijri_date"].strip():
                context_data["hijri_date"] = hijri_date_str
            if "اليوم" not in context_data or not context_data["اليوم"].strip():
                context_data["اليوم"] = day_name_arabic
            if "day" not in context_data or not context_data["day"].strip():
                context_data["day"] = day_name_arabic

            # إضافة بيانات الأصناف إذا كان القالب يدعمها
            if self.template_has_items_table.get(template_name, False):
                context_data['items'] = self.items_data
                context_data['total'] = self.calculate_total_items_price()

            # --- دمج QR Code للمعاينة ---
            seller_name = "اسم البائع الافتراضي"
            vat_registration_no = "300000000000003"

            total_amount_str = self.variable_entries.get(self.find_total_field(), tk.Entry()).get().strip()
            vat_amount_str = self.variable_entries.get(self.find_tax_field(), tk.Entry()).get().strip()

            total_amount = float(self.clean_numeric_value(total_amount_str)) if total_amount_str else 0.0
            vat_amount = float(self.clean_numeric_value(vat_amount_str)) if vat_amount_str else 0.0

            invoice_data = {
                "seller_name": seller_name,
                "vat_registration_no": vat_registration_no,
                "timestamp": datetime.now().isoformat() + "Z",
                "total_amount": total_amount,
                "vat_amount": vat_amount
            }
            
            if ZATCA_AVAILABLE:
                qr_base64, qr_image_pil = validate_and_generate_qr(
                    seller_name, vat_registration_no, str(total_amount), str(vat_amount)
                )
                if not qr_image_pil:
                    raise Exception("فشل في توليد QR Code")
            else:
                raise Exception("ZATCA generator غير متاح")
            
            # حفظ الصورة في ملف مؤقت لاستخدامها في DocxTemplate
            qr_code_temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
            qr_image_pil.save(qr_code_temp_file.name)
            qr_code_temp_file.close()
            qr_code_temp_file_path = qr_code_temp_file.name

            # يجب أن يتم تمرير doc هنا لـ InlineImage
            # بما أن doc يتم إنشاؤه داخل create_temp_docx_with_data، سنحتاج لتعديلها لتمرير doc
            # أو إنشاء doc هنا وتمريره إلى create_temp_docx_with_data
            
            doc = DocxTemplate(file_path)
            # إضافة صورة QR Code بحجم 1.7 بوصة (43.18 مم)
            context_data['qr_code_image'] = InlineImage(doc, qr_code_temp_file_path, width=Mm(43.18), height=Mm(43.18))
            self.temp_files_to_cleanup.append(qr_code_temp_file_path) # إضافة الملف المؤقت للتنظيف

            print("Items data before rendering (preview):", self.items_data)
            # إنشاء ملف Word مؤقت
            return self.create_temp_docx_with_data(doc, context_data)
            
        except Exception as e:
            print(f"خطأ في إنشاء ملف المعاينة: {e}")
            if qr_code_temp_file and os.path.exists(qr_code_temp_file.name):
                os.remove(qr_code_temp_file.name)
            return None

    def on_closing(self):
        """تنظيف الملفات المؤقتة عند إغلاق التطبيق"""
        try:
            # تنظيف الملفات المؤقتة
            for temp_file in self.temp_files_to_cleanup:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            
            # تنظيف ملف HTML المؤقت
            if hasattr(self, 'temp_html_file') and self.temp_html_file and os.path.exists(self.temp_html_file):
                os.remove(self.temp_html_file)
                
        except Exception as e:
            print(f"خطأ في تنظيف الملفات المؤقتة: {e}")
        
        # إغلاق قاعدة البيانات
        if self.db_conn:
            self.db_conn.close()
            
        # إغلاق التطبيق
        self.root.destroy()

    def append_to_documentation_archive(self, title, content):
        """Appends a new entry to the documentation archive file."""
        archive_path = "documentation_archive.md"
        try:
            with open(archive_path, "a", encoding="utf-8") as f:
                f.write(f"\n\n---\n## {title} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n---\n\n")
                f.write(content)
                f.write("\n")
            self.update_status(f"✅ تم تسجيل معلومة جديدة في ملف الأرشيف: {title}")
        except Exception as e:
            messagebox.showerror("خطأ في الأرشفة", f"فشل في الكتابة إلى ملف الأرشيف:\n{e}")

    def calculate_total_items_price(self):
        """حساب الإجمالي الكلي لأسعار الأصناف"""
        total = 0.0
        for item in self.items_data:
            try:
                qty_str = self.clean_numeric_value(str(item.get('qty', '0')))
                price_str = self.clean_numeric_value(str(item.get('price', '0')))
                qty = float(qty_str)
                price = float(price_str)
                total += qty * price
            except (ValueError, TypeError):
                continue # تجاهل الأصناف ذات القيم غير الرقمية
        return total

    def generate_contract(self):
        client_name = self.client_combo.get()
        template_name = self.template_combo.get()

        if not client_name or not template_name:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل وقالب أولاً.")
            return

        doc = None # تهيئة doc إلى None
        qr_code_temp_file_path = None # تهيئة مسار ملف QR المؤقت
        output_filename = f"{client_name}_{template_name.replace('.docx', '')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"

        # توليد رقم الاتفاقية المتسلسل فقط لعقود الصيانة أو التركيب والتوريد
        agreement_number = None
        if self.is_maintenance_or_installation_contract(template_name):
            agreement_number = self.get_next_agreement_number()

        try:
            # تحميل القالب هنا ليكون doc متاحًا لـ InlineImage
            template_path = self.template_path_map.get(template_name)
            if not template_path or not os.path.exists(template_path):
                raise FileNotFoundError("مسار القالب غير صالح أو الملف غير موجود.")
            doc = DocxTemplate(template_path)

            # جمع البيانات من حقول الإدخال
            data = {}
            for var, entry in self.variable_entries.items():
                data[var] = entry.get()

            # دمج بيانات العميل مع البيانات المدخلة
            context = {**self.current_client_data, **data}

            # إضافة بيانات التاريخ التلقائية
            today = datetime.now()
            context["gregorian_date"] = today.strftime("%d/%m/%Y")
            hijri_date = Gregorian(today.year, today.month, today.day).to_hijri()
            context["hijri_date"] = f"{hijri_date.day}/{hijri_date.month}/{hijri_date.year}"
            arabic_day_names = {
                "Saturday": "السبت", "Sunday": "الأحد", "Monday": "الإثنين", "Tuesday": "الثلاثاء",
                "Wednesday": "الأربعاء", "Thursday": "الخميس", "Friday": "الجمعة"
            }
            context["day"] = arabic_day_names.get(today.strftime("%A"), today.strftime("%A"))

            # إضافة جدول الأصناف إذا كان القالب يدعمه
            if self.template_has_items_table.get(template_name, False):
                context['items'] = self.items_data
                # حساب الإجمالي الكلي للأصناف
                total_items_amount = self.calculate_total_items_price()
                context['total'] = self.format_currency(total_items_amount)
                # يمكنك إضافة متغيرات أخرى مثل إجمالي الضريبة للأصناف هنا إذا لزم الأمر

            # --- دمج QR Code ---
            # بيانات افتراضية للبائع (يجب أن تكون قابلة للتكوين)
            seller_name = "اسم البائع الافتراضي"
            vat_registration_no = "300000000000003" # رقم التسجيل الضريبي الافتراضي

            # الحصول على قيم المبلغ الإجمالي والضريبة من حقول المتغيرات
            total_amount_str = self.variable_entries.get(self.find_total_field(), tk.Entry()).get().strip()
            vat_amount_str = self.variable_entries.get(self.find_tax_field(), tk.Entry()).get().strip()

            # تنظيف القيم الرقمية
            total_amount = float(self.clean_numeric_value(total_amount_str)) if total_amount_str else 0.0
            vat_amount = float(self.clean_numeric_value(vat_amount_str)) if vat_amount_str else 0.0

            invoice_data = {
                "seller_name": seller_name,
                "vat_registration_no": vat_registration_no,
                "timestamp": datetime.now().isoformat() + "Z",
                "total_amount": total_amount,
                "vat_amount": vat_amount
            }
            
            # توليد QR Code
            if ZATCA_AVAILABLE:
                qr_image_pil = validate_and_generate_qr(
                    seller_name=seller_name,
                    vat_number=vat_registration_no,
                    timestamp=invoice_data["timestamp"],
                    total_amount=str(total_amount),
                    vat_amount=str(vat_amount)
                )
                if not qr_image_pil:
                    raise Exception("فشل في توليد QR Code")
            else:
                raise Exception("ZATCA generator غير متاح")
            
            # حفظ الصورة في ملف مؤقت لاستخدامها في DocxTemplate
            qr_code_temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
            qr_image_pil.save(qr_code_temp_file.name)
            qr_code_temp_file.close()
            qr_code_temp_file_path = qr_code_temp_file.name

            # إضافة الصورة إلى سياق القالب بحجم 1.7 بوصة (43.18 مم × 43.18 مم)
            context['qr_code_image'] = InlineImage(doc, qr_code_temp_file_path, width=Mm(43.18), height=Mm(43.18))
            self.temp_files_to_cleanup.append(qr_code_temp_file_path) # إضافة الملف المؤقت للتنظيف

            # --- نهاية دمج QR Code ---

            doc.render(context)

            # حفظ العقد النهائي
            output_filename = f"{client_name}_{template_name.replace('.docx', '')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            output_path = os.path.join(ARCHIVE_DIR, output_filename)
            doc.save(output_path)

            # حفظ بيانات العقد في قاعدة البيانات مع رقم الاتفاقية
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            contract_data = json.dumps(context, ensure_ascii=False)
            self.cursor.execute(
                "INSERT INTO contracts (agreement_number, client_id, template_id, contract_data, word_file_path, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                (
                    agreement_number,
                    self.client_data_map.get(client_name),
                    self.template_id_map.get(template_name),
                    contract_data,
                    output_path,
                    created_at
                )
            )
            self.db_conn.commit()
            # عرض رسالة النجاح بشكل صحيح حسب وجود رقم الاتفاقية
            if agreement_number:
                messagebox.showinfo("نجاح", f"تم توليد العقد بنجاح وحفظه في:\n{output_path}\nرقم الاتفاقية: {agreement_number}")
                self.update_status(f"✅ تم توليد العقد: {output_filename} (رقم الاتفاقية: {agreement_number})")
            else:
                messagebox.showinfo("نجاح", f"تم توليد العقد بنجاح وحفظه في:\n{output_path}")
                self.update_status(f"✅ تم توليد العقد: {output_filename}")

            # فتح المجلد الذي يحتوي على العقد
            if sys.platform == "win32":
                os.startfile(ARCHIVE_DIR)
            elif sys.platform == "darwin": # macOS
                subprocess.Popen(["open", ARCHIVE_DIR])
            else: # Linux
                subprocess.Popen(["xdg-open", ARCHIVE_DIR])

        except Exception as e:
            messagebox.showerror("خطأ", f"""فشل في توليد العقد:
{e}""")
            self.update_status(f"❌ فشل توليد العقد: {e}", icon="🔴")
            # تسجيل الخطأ في الأرشيف
            error_title = f"خطأ في توليد العقد: {output_filename}"
            error_content = f"""**الخطأ:**
```
{e}
```"""
            self.append_to_documentation_archive(error_title, error_content)
            # إذا فشل توليد QR Code، تأكد من أن المتغير فارغ
            if 'qr_code_image' in context:
                context['qr_code_image'] = ""
        finally:
            # تنظيف الملف المؤقت لـ QR Code
            if qr_code_temp_file_path and os.path.exists(qr_code_temp_file_path):
                os.remove(qr_code_temp_file_path)

    def calculate_total_items_price(self):
        """حساب الإجمالي الكلي لأسعار الأصناف"""
        total = 0.0
        for item in self.items_data:
            try:
                qty_str = self.clean_numeric_value(str(item.get('qty', '0')))
                price_str = self.clean_numeric_value(str(item.get('price', '0')))
                qty = float(qty_str)
                price = float(price_str)
                total += qty * price
            except (ValueError, TypeError):
                continue # تجاهل الأصناف ذات القيم غير الرقمية
        return total

    def generate_preview_contract(self):
        """إنشاء نسخة Word من المعاينة"""
        try:
            temp_docx_path = self.create_current_preview_docx()
            if not temp_docx_path:
                messagebox.showerror("خطأ", "فشل في إنشاء ملف المعاينة")
                return
            
            # حفظ الملف في مجلد الأرشيف
            client_name = self.client_combo.get()
            template_name = self.template_combo.get()
            client_display_name = self.current_client_data.get("name", "غير معروف").replace(" ", "_").replace("/", "_")
            output_filename = f"معاينة_{client_display_name}_{template_name.replace('.docx', '')}_temp.docx"
            output_path = os.path.join(ARCHIVE_DIR, "preview", output_filename)
            
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            shutil.copy(temp_docx_path, output_path)
            
            messagebox.showinfo("نجاح", f"""تم حفظ نسخة المعاينة بنجاح في:
{output_path}""")
            self.open_file(output_path)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المعاينة: {e}")

    def open_file(self, path):
        """فتح ملف باستخدام التطبيق الافتراضي للنظام"""
        try:
            if os.name == 'nt': # Windows
                os.startfile(path)
            elif sys.platform == 'darwin': # macOS
                subprocess.Popen(['open', path])
            else: # Linux
                subprocess.Popen(['xdg-open', path])
        except Exception as e:
            messagebox.showerror("خطأ", f"""فشل في فتح الملف: {path}
{e}""")



    # --- دوال QR الزكاة ---

    def update_zatca_fields_from_contract(self):
        """تحديث حقول مولد الزكاة تلقائياً من بيانات العقد"""
        if not hasattr(self, 'zatca_entries') or not self.zatca_section_frame.winfo_ismapped():
            return

        final_total_field_name = self.find_final_total_field()
        tax_field_name = self.find_tax_field()

        final_total_value = ""
        if final_total_field_name and final_total_field_name in self.variable_entries:
            final_total_value = self.variable_entries[final_total_field_name].get().strip()

        tax_value = ""
        if tax_field_name and tax_field_name in self.variable_entries:
            tax_value = self.variable_entries[tax_field_name].get().strip()

        if "invoice_total" in self.zatca_entries:
            invoice_total_entry = self.zatca_entries["invoice_total"]
            invoice_total_entry.delete(0, tk.END)
            invoice_total_entry.insert(0, final_total_value)

        if "vat_total" in self.zatca_entries:
            vat_total_entry = self.zatca_entries["vat_total"]
            vat_total_entry.delete(0, tk.END)
            vat_total_entry.insert(0, tax_value)

    def generate_zatca_qr_ui(self):
        """توليد QR الزكاة من واجهة المستخدم"""
        try:
            # الحصول على البيانات من الحقول
            seller_name = self.zatca_entries["seller_name"].get().strip()
            vat_number = self.zatca_entries["vat_number"].get().strip()
            invoice_total = self.zatca_entries["invoice_total"].get().strip()
            vat_total = self.zatca_entries["vat_total"].get().strip()
            
            # التحقق من البيانات
            if not all([seller_name, vat_number, invoice_total, vat_total]):
                messagebox.showwarning("تنبيه", "جميع الحقول مطلوبة!")
                return
            
            # التحقق من صحة الأرقام
            try:
                float(invoice_total)
                float(vat_total)
            except ValueError:
                messagebox.showerror("خطأ", "يجب أن تكون قيم الفاتورة والضريبة أرقاماً صحيحة!")
                return
            
            # توليد التاريخ الحالي
            invoice_datetime = datetime.now().isoformat()
            
            # توليد QR
            base64_qr = self.generate_zatca_qr_code(seller_name, vat_number, invoice_datetime, 
                                                  invoice_total, vat_total)
            
            # عرض النتيجة
            # self.zatca_output_text.delete(1.0, tk.END)
            # self.zatca_output_text.insert(1.0, base64_qr)
            
            # تحديث الحالة
            self.qr_status_label.config(text="✅ تم توليد QR بنجاح!")
            self.open_qr_btn.config(state="normal")
            
            messagebox.showinfo("نجح!", "تم توليد QR Code بنجاح!\nتم حفظ الملف: zatca_qr.png")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء توليد QR:\n{str(e)}")
            self.qr_status_label.config(text="❌ فشل في توليد QR")

    def generate_zatca_qr_code(self, seller_name, vat_number, invoice_datetime, invoice_total, vat_total):
        """توليد QR الزكاة - الكود الأساسي"""
        try:
            import qrcode
            import base64
            
            def to_tlv(tag, value):
                value_bytes = value.encode('utf-8')
                return bytes([tag]) + bytes([len(value_bytes)]) + value_bytes
            
            # Build TLV structure
            tlv_bytes = b''
            tlv_bytes += to_tlv(1, seller_name)
            tlv_bytes += to_tlv(2, vat_number)
            tlv_bytes += to_tlv(3, invoice_datetime)
            tlv_bytes += to_tlv(4, invoice_total)
            tlv_bytes += to_tlv(5, vat_total)
            
            # Encode TLV as Base64
            base64_tlv = base64.b64encode(tlv_bytes).decode('utf-8')
            
            # Generate QR code
            qr = qrcode.QRCode(version=1, box_size=10, border=4)
            qr.add_data(base64_tlv)
            qr.make(fit=True)
            img = qr.make_image(fill='black', back_color='white')
            
            # Save QR code image
            qr_path = os.path.join(os.getcwd(), 'zatca_qr.png')
            img.save(qr_path)
            
            return base64_tlv
        except ImportError:
            raise Exception("مكتبة qrcode غير مثبتة. يرجى تثبيتها باستخدام: pip install qrcode[pil]")

    def clear_zatca_fields(self):
        """مسح حقول QR الزكاة"""
        defaults = {
            "seller_name": "مؤسسة اَر اَر أو للمصاعد",
            "vat_number": "312853107300003",
            "invoice_total": "100.00",
            "vat_total": "15.00"
        }
        
        for key, entry in self.zatca_entries.items():
            entry.delete(0, tk.END)
            entry.insert(0, defaults.get(key, ""))
        
        self.qr_status_label.config(text="📍 الحالة: جاهز لتوليد QR")
        self.open_qr_btn.config(state="disabled")

    def open_qr_file(self):
        """فتح ملف QR المولد"""
        qr_path = os.path.join(os.getcwd(), 'zatca_qr.png')
        if os.path.exists(qr_path):
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(qr_path)
                elif os.name == 'posix':  # macOS and Linux
                    subprocess.call(['open', qr_path])
            except Exception as e:
                messagebox.showerror("خطأ", f"لا يمكن فتح الملف:\n{str(e)}")
        else:
            messagebox.showwarning("تنبيه", "ملف QR غير موجود!")

    def save_zatca_settings_ui(self):
        """حفظ إعدادات QR الزكاة"""
        try:
            # حفظ الإعدادات في ملف JSON
            settings = {}
            for key, entry in self.zatca_entries.items():
                settings[key] = entry.get()
            
            settings_path = os.path.join(os.getcwd(), 'zatca_settings.json')
            import json
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("نجح!", f"تم حفظ الإعدادات في:\n{settings_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

    def load_zatca_settings_silently(self):
        """تحميل إعدادات QR الزكاة المحفوظة بصمت (بدون رسائل)"""
        try:
            settings_path = os.path.join(os.getcwd(), 'zatca_settings.json')
            if os.path.exists(settings_path):
                import json
                with open(settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                for key, value in settings.items():
                    if key in self.zatca_entries:
                        self.zatca_entries[key].delete(0, tk.END)
                        self.zatca_entries[key].insert(0, value)
                
        except Exception as e:
            # تحميل صامت - لا نعرض رسائل خطأ
            pass

    def load_zatca_settings_ui(self):
        """تحميل إعدادات QR الزكاة المحفوظة"""
        try:
            settings_path = os.path.join(os.getcwd(), 'zatca_settings.json')
            if os.path.exists(settings_path):
                import json
                with open(settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                for key, value in settings.items():
                    if key in self.zatca_entries:
                        self.zatca_entries[key].delete(0, tk.END)
                        self.zatca_entries[key].insert(0, value)
                
                pass  # تم تعطيل الرسائل المنبثقة عند تحميل إعدادات الزكاة
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإعدادات:\n{str(e)}")



if __name__ == "__main__":
    root = tk.Tk()
    app = SmartContractApp(root)
    root.mainloop()
