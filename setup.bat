@echo off
echo ========================================
echo    إعداد نظام إدارة العقود الذكي
echo ========================================
echo.

echo الخطوة 1: فحص النظام...
python check_system.py

echo.
echo الخطوة 2: تثبيت المتطلبات...
@echo off
echo ========================================
echo    تثبيت متطلبات نظام إدارة العقود الذكي
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python أولاً من: https://python.org
    pause
    exit /b 1
)

echo تم العثور على Python...
echo.

REM التأكد من وجود pip
echo التحقق من pip...
python -m ensurepip --upgrade

REM تثبيت المتطلبات من ملف requirements.txt
echo.
echo تثبيت المكتبات المطلوبة...
echo.

if exist requirements.txt (
    pip install -r requirements.txt --upgrade
) else (
    echo تثبيت المكتبات يدوياً...
    pip install docxtpl --upgrade
    pip install hijri-converter --upgrade
    pip install python-docx --upgrade
    pip install mammoth --upgrade
    pip install Pillow --upgrade
    pip install tkhtmlview --upgrade
)

echo.
echo ========================================
echo تم تثبيت جميع المتطلبات بنجاح!
echo يمكنك الآن تشغيل التطبيق باستخدام run.bat
echo ========================================

echo.
echo الخطوة 3: إنشاء قاعدة البيانات...
python create_database.py

echo.
echo الخطوة 4: اختبار النظام...
python test_tkinter.py

echo.
echo الخطوة 5: فحص نهائي...
python check_system.py

echo.
echo ========================================
echo تم إعداد النظام بنجاح!
echo.
echo الملفات المتاحة:
echo - run.bat          : تشغيل التطبيق
echo - check_system.py  : فحص النظام
echo - README.md        : دليل الاستخدام
echo - QUICK_START.md   : دليل البدء السريع
echo.
echo يمكنك الآن تشغيل التطبيق باستخدام run.bat
echo ========================================
pause