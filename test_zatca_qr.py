#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظيفة توليد QR الزكاة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import qrcode
import base64
from datetime import datetime
import os
import subprocess

class ZatcaQRTester:
    def __init__(self, root):
        self.root = root
        self.root.title("اختبار مولد QR الزكاة")
        self.root.geometry("600x500")
        self.root.configure(bg="#f0f0f0")
        
        # إعداد الخطوط
        self.fonts = {
            'title': ('Arial', 16, 'bold'),
            'heading': ('Arial', 12, 'bold'),
            'body': ('Arial', 10)
        }
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = tk.Label(self.root, text="🔳 مولد QR الزكاة والضريبة", 
                              font=self.fonts['title'], bg="#f0f0f0")
        title_label.pack(pady=20)
        
        # إطار الحقول
        fields_frame = ttk.LabelFrame(self.root, text="بيانات الفاتورة", padding="20")
        fields_frame.pack(fill="x", padx=20, pady=10)
        
        # حقول البيانات
        self.entries = {}
        
        fields = [
            {"key": "seller_name", "label": "اسم البائع:", "default": "مؤسسة اَر اَر أو للمصاعد"},
            {"key": "vat_number", "label": "الرقم الضريبي:", "default": "312853107300003"},
            {"key": "invoice_total", "label": "إجمالي الفاتورة:", "default": "100.00"},
            {"key": "vat_total", "label": "قيمة الضريبة:", "default": "15.00"}
        ]
        
        for i, field in enumerate(fields):
            label = tk.Label(fields_frame, text=field["label"], font=self.fonts['heading'])
            label.grid(row=i, column=0, sticky="e", padx=(0, 10), pady=5)
            
            entry = tk.Entry(fields_frame, font=self.fonts['body'], width=30)
            entry.insert(0, field["default"])
            entry.grid(row=i, column=1, sticky="ew", pady=5)
            
            self.entries[field["key"]] = entry
        
        fields_frame.grid_columnconfigure(1, weight=1)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg="#f0f0f0")
        buttons_frame.pack(fill="x", padx=20, pady=10)
        
        # زر توليد QR
        generate_btn = tk.Button(buttons_frame, text="🔳 توليد QR Code", 
                               font=self.fonts['heading'], bg="#4CAF50", fg="white",
                               command=self.generate_qr)
        generate_btn.pack(side="right", padx=(10, 0))
        
        # زر مسح الحقول
        clear_btn = tk.Button(buttons_frame, text="🗑️ مسح الحقول", 
                            font=self.fonts['heading'], bg="#f44336", fg="white",
                            command=self.clear_fields)
        clear_btn.pack(side="right")
        
        # إطار النتائج
        results_frame = ttk.LabelFrame(self.root, text="النتائج", padding="10")
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # منطقة النص للـ Base64
        tk.Label(results_frame, text="Base64 TLV Data:", font=self.fonts['heading']).pack(anchor="w")
        
        self.output_text = tk.Text(results_frame, height=6, wrap=tk.WORD, 
                                 font=self.fonts['body'])
        self.output_text.pack(fill="both", expand=True, pady=(5, 10))
        
        # إطار معلومات QR
        info_frame = tk.Frame(results_frame, bg="white")
        info_frame.pack(fill="x")
        
        self.status_label = tk.Label(info_frame, text="📍 الحالة: جاهز لتوليد QR", 
                                   font=self.fonts['body'], bg="white")
        self.status_label.pack(side="left")
        
        # زر فتح ملف QR
        self.open_btn = tk.Button(info_frame, text="📂 فتح ملف QR", 
                                font=self.fonts['body'], bg="#2196F3", fg="white",
                                command=self.open_qr_file, state="disabled")
        self.open_btn.pack(side="right")
    
    def generate_qr(self):
        """توليد QR الزكاة"""
        try:
            # الحصول على البيانات من الحقول
            seller_name = self.entries["seller_name"].get().strip()
            vat_number = self.entries["vat_number"].get().strip()
            invoice_total = self.entries["invoice_total"].get().strip()
            vat_total = self.entries["vat_total"].get().strip()
            
            # التحقق من البيانات
            if not all([seller_name, vat_number, invoice_total, vat_total]):
                messagebox.showwarning("تنبيه", "جميع الحقول مطلوبة!")
                return
            
            # التحقق من صحة الأرقام
            try:
                float(invoice_total)
                float(vat_total)
            except ValueError:
                messagebox.showerror("خطأ", "يجب أن تكون قيم الفاتورة والضريبة أرقاماً صحيحة!")
                return
            
            # توليد التاريخ الحالي
            invoice_datetime = datetime.now().isoformat()
            
            # توليد QR
            base64_qr = self.generate_zatca_qr_code(seller_name, vat_number, invoice_datetime, 
                                                  invoice_total, vat_total)
            
            # عرض النتيجة
            self.output_text.delete(1.0, tk.END)
            self.output_text.insert(1.0, base64_qr)
            
            # تحديث الحالة
            self.status_label.config(text="✅ تم توليد QR بنجاح!")
            self.open_btn.config(state="normal")
            
            messagebox.showinfo("نجح!", "تم توليد QR Code بنجاح!\nتم حفظ الملف: zatca_qr.png")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء توليد QR:\n{str(e)}")
            self.status_label.config(text="❌ فشل في توليد QR")
    
    def generate_zatca_qr_code(self, seller_name, vat_number, invoice_datetime, invoice_total, vat_total):
        """توليد QR الزكاة - الكود الأساسي"""
        def to_tlv(tag, value):
            value_bytes = value.encode('utf-8')
            return bytes([tag]) + bytes([len(value_bytes)]) + value_bytes
        
        # Build TLV structure
        tlv_bytes = b''
        tlv_bytes += to_tlv(1, seller_name)
        tlv_bytes += to_tlv(2, vat_number)
        tlv_bytes += to_tlv(3, invoice_datetime)
        tlv_bytes += to_tlv(4, invoice_total)
        tlv_bytes += to_tlv(5, vat_total)
        
        # Encode TLV as Base64
        base64_tlv = base64.b64encode(tlv_bytes).decode('utf-8')
        
        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=4)
        qr.add_data(base64_tlv)
        qr.make(fit=True)
        img = qr.make_image(fill='black', back_color='white')
        
        # Save QR code image
        qr_path = os.path.join(os.getcwd(), 'zatca_qr.png')
        img.save(qr_path)
        
        return base64_tlv
    
    def clear_fields(self):
        """مسح حقول البيانات"""
        defaults = {
            "seller_name": "مؤسسة اَر اَر أو للمصاعد",
            "vat_number": "312853107300003",
            "invoice_total": "100.00",
            "vat_total": "15.00"
        }
        
        for key, entry in self.entries.items():
            entry.delete(0, tk.END)
            entry.insert(0, defaults.get(key, ""))
        
        self.output_text.delete(1.0, tk.END)
        self.status_label.config(text="📍 الحالة: جاهز لتوليد QR")
        self.open_btn.config(state="disabled")
    
    def open_qr_file(self):
        """فتح ملف QR المولد"""
        qr_path = os.path.join(os.getcwd(), 'zatca_qr.png')
        if os.path.exists(qr_path):
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(qr_path)
                elif os.name == 'posix':  # macOS and Linux
                    subprocess.call(['open', qr_path])
            except Exception as e:
                messagebox.showerror("خطأ", f"لا يمكن فتح الملف:\n{str(e)}")
        else:
            messagebox.showwarning("تنبيه", "ملف QR غير موجود!")

if __name__ == "__main__":
    root = tk.Tk()
    app = ZatcaQRTester(root)
    root.mainloop()